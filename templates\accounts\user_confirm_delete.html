{% extends 'base.html' %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'accounts:user_list' %}">Utilisateurs</a></li>
                <li class="breadcrumb-item"><a href="{% url 'accounts:user_detail' user_detail.pk %}">{{ user_detail.get_full_name|default:user_detail.username }}</a></li>
                <li class="breadcrumb-item active">Désactiver</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 
                    Confirmation de désactivation
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-circle-large mx-auto mb-3">
                        <i class="bi bi-person-x"></i>
                    </div>
                    <h4>{{ user_detail.get_full_name|default:user_detail.username }}</h4>
                    <p class="text-muted">{{ user_detail.get_role_display }}</p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="bi bi-info-circle"></i> Attention :</h6>
                    <p class="mb-0">
                        Vous êtes sur le point de <strong>désactiver</strong> cet utilisateur. 
                        Cette action aura les conséquences suivantes :
                    </p>
                </div>
                
                <ul class="list-group list-group-flush mb-4">
                    <li class="list-group-item d-flex align-items-center">
                        <i class="bi bi-x-circle text-danger me-2"></i>
                        L'utilisateur ne pourra plus se connecter
                    </li>
                    <li class="list-group-item d-flex align-items-center">
                        <i class="bi bi-pause-circle text-warning me-2"></i>
                        Ses sessions actives seront terminées
                    </li>
                    <li class="list-group-item d-flex align-items-center">
                        <i class="bi bi-archive text-info me-2"></i>
                        Ses données seront conservées
                    </li>
                    <li class="list-group-item d-flex align-items-center">
                        <i class="bi bi-arrow-clockwise text-success me-2"></i>
                        Le compte peut être réactivé plus tard
                    </li>
                </ul>
                
                <!-- Informations utilisateur -->
                <div class="card bg-light">
                    <div class="card-body">
                        <h6>Informations de l'utilisateur :</h6>
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Email :</strong><br>
                                <small class="text-muted">{{ user_detail.email|default:"-" }}</small>
                            </div>
                            <div class="col-sm-6">
                                <strong>Département :</strong><br>
                                <small class="text-muted">{{ user_detail.department|default:"-" }}</small>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-6">
                                <strong>Inscription :</strong><br>
                                <small class="text-muted">{{ user_detail.date_joined|date:"d/m/Y" }}</small>
                            </div>
                            <div class="col-sm-6">
                                <strong>Dernière connexion :</strong><br>
                                <small class="text-muted">
                                    {% if user_detail.last_login %}
                                        {{ user_detail.last_login|date:"d/m/Y H:i" }}
                                    {% else %}
                                        Jamais
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form method="post" class="mt-4">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:user_detail' user_detail.pk %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-person-x"></i> Confirmer la désactivation
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Note importante -->
        <div class="alert alert-info mt-3">
            <h6><i class="bi bi-lightbulb"></i> Note importante :</h6>
            <p class="mb-0 small">
                La désactivation est une action réversible. Si vous souhaitez supprimer définitivement 
                les données de l'utilisateur, contactez l'administrateur système.
            </p>
        </div>
    </div>
</div>

<style>
.avatar-circle-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}
</style>
{% endblock %}
