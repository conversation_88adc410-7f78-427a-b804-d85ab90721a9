from django.contrib import admin
from .models import ProductCategory, Product, Warehouse, Stock, StockTransfer, StockTransferLine


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name',)
    ordering = ('name',)


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'reference', 'category', 'unit', 'minimum_stock', 'is_active')
    list_filter = ('category', 'is_active', 'unit')
    search_fields = ('name', 'reference')
    ordering = ('name',)


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'warehouse_type', 'manager', 'is_active')
    list_filter = ('warehouse_type', 'is_active')
    search_fields = ('name', 'code')
    ordering = ('name',)


@admin.register(Stock)
class StockAdmin(admin.ModelAdmin):
    list_display = ('product', 'warehouse', 'quantity', 'reserved_quantity', 'available_quantity', 'is_low_stock')
    list_filter = ('warehouse', 'product__category')
    search_fields = ('product__name', 'warehouse__name')
    ordering = ('warehouse', 'product')
    readonly_fields = ('available_quantity', 'is_low_stock')


class StockTransferLineInline(admin.TabularInline):
    model = StockTransferLine
    extra = 1


@admin.register(StockTransfer)
class StockTransferAdmin(admin.ModelAdmin):
    list_display = ('number', 'from_warehouse', 'to_warehouse', 'transfer_date', 'status', 'requested_by')
    list_filter = ('status', 'from_warehouse', 'to_warehouse')
    search_fields = ('number',)
    ordering = ('-created_at',)
    inlines = [StockTransferLineInline]
