from django import forms
from django.forms import inlineformset_factory
from .models import Reception, ReceptionLine, Document
from procurement.models import PurchaseOrder
from inventory.models import Warehouse


class ReceptionForm(forms.ModelForm):
    """Formulaire pour les réceptions"""
    class Meta:
        model = Reception
        fields = ['number', 'purchase_order', 'warehouse', 'reception_date', 'delivery_note_number', 'notes']
        widgets = {
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'purchase_order': forms.Select(attrs={'class': 'form-control'}),
            'warehouse': forms.Select(attrs={'class': 'form-control'}),
            'reception_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'delivery_note_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les bons de commande qui peuvent être réceptionnés
        self.fields['purchase_order'].queryset = PurchaseOrder.objects.filter(
            status__in=['sent', 'confirmed', 'partially_received']
        ).select_related('supplier')
        
        # Filtrer les magasins centraux
        self.fields['warehouse'].queryset = Warehouse.objects.filter(
            warehouse_type='central',
            is_active=True
        )


class ReceptionLineForm(forms.ModelForm):
    """Formulaire pour les lignes de réception"""
    class Meta:
        model = ReceptionLine
        fields = ['product', 'quantity_received', 'notes']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'quantity_received': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }
    
    def __init__(self, *args, **kwargs):
        purchase_order = kwargs.pop('purchase_order', None)
        super().__init__(*args, **kwargs)
        
        if purchase_order:
            # Limiter les produits à ceux du bon de commande
            product_ids = purchase_order.lines.values_list('product_id', flat=True)
            self.fields['product'].queryset = self.fields['product'].queryset.filter(
                id__in=product_ids
            )


# Formset pour les lignes de réception
ReceptionLineFormSet = inlineformset_factory(
    Reception,
    ReceptionLine,
    form=ReceptionLineForm,
    extra=0,
    can_delete=False,
    min_num=1,
    validate_min=True
)


class DocumentUploadForm(forms.ModelForm):
    """Formulaire pour l'upload de documents"""
    file = forms.FileField(
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.jpg,.jpeg,.png'})
    )
    
    class Meta:
        model = Document
        fields = ['name', 'document_type', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'document_type': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['description'].required = False
    
    def save(self, commit=True):
        document = super().save(commit=False)
        
        # Traiter le fichier uploadé
        uploaded_file = self.cleaned_data['file']
        document.file_data = uploaded_file.read()
        document.file_name = uploaded_file.name
        document.file_size = uploaded_file.size
        document.mime_type = uploaded_file.content_type or 'application/octet-stream'
        
        if commit:
            document.save()
        return document


class ReceptionSearchForm(forms.Form):
    """Formulaire de recherche pour les réceptions"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par numéro, BC...'
        })
    )
    purchase_order = forms.ModelChoiceField(
        queryset=PurchaseOrder.objects.all(),
        required=False,
        empty_label="Tous les BC",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + Reception.RECEPTION_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(warehouse_type='central', is_active=True),
        required=False,
        empty_label="Tous les magasins",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class StockAdjustmentForm(forms.Form):
    """Formulaire pour l'ajustement de stock"""
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Magasin"
    )
    product = forms.ModelChoiceField(
        queryset=None,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Produit"
    )
    adjustment_type = forms.ChoiceField(
        choices=[
            ('increase', 'Augmentation'),
            ('decrease', 'Diminution'),
            ('set', 'Définir quantité exacte')
        ],
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Type d'ajustement"
    )
    quantity = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
        label="Quantité"
    )
    reason = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        label="Motif de l'ajustement"
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filtrer les magasins selon les permissions de l'utilisateur
            if user.role == 'sub_storekeeper':
                self.fields['warehouse'].queryset = Warehouse.objects.filter(
                    manager=user,
                    is_active=True
                )
            elif user.role == 'central_storekeeper':
                self.fields['warehouse'].queryset = Warehouse.objects.filter(
                    warehouse_type='central',
                    is_active=True
                )
        
        # Initialiser la queryset des produits (sera mise à jour via AJAX)
        from inventory.models import Product
        self.fields['product'].queryset = Product.objects.filter(is_active=True)
