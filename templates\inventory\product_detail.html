{% extends 'base.html' %}

{% block title %}{{ product.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:product_list' %}">Produits</a></li>
                <li class="breadcrumb-item active">{{ product.name }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-box-seam"></i> 
                {{ product.name }}
                {% if not product.is_active %}
                    <span class="badge bg-secondary ms-2">Inactif</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                {% if user.can_manage_central_warehouse %}
                <a href="{% url 'inventory:product_edit' product.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations du produit -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations du produit</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Référence :</dt>
                            <dd class="col-sm-8"><code>{{ product.reference }}</code></dd>
                            
                            <dt class="col-sm-4">Nom :</dt>
                            <dd class="col-sm-8"><strong>{{ product.name }}</strong></dd>
                            
                            <dt class="col-sm-4">Catégorie :</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-info">{{ product.category.name }}</span>
                            </dd>
                            
                            <dt class="col-sm-4">Unité :</dt>
                            <dd class="col-sm-8">{{ product.unit }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Stock minimum :</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-warning">{{ product.minimum_stock }} {{ product.unit }}</span>
                            </dd>
                            
                            <dt class="col-sm-5">Statut :</dt>
                            <dd class="col-sm-7">
                                {% if product.is_active %}
                                    <span class="badge bg-success">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactif</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Créé le :</dt>
                            <dd class="col-sm-7">{{ product.created_at|date:"d/m/Y à H:i" }}</dd>
                            
                            <dt class="col-sm-5">Modifié le :</dt>
                            <dd class="col-sm-7">{{ product.updated_at|date:"d/m/Y à H:i" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if product.description %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6>Description :</h6>
                        <p class="text-muted">{{ product.description|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Stock par magasin -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-graph-up"></i> Stock par magasin</h5>
                <span class="badge bg-primary fs-6">Total : {{ total_stock }} {{ product.unit }}</span>
            </div>
            <div class="card-body">
                {% if stocks %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Magasin</th>
                                <th>Type</th>
                                <th>Quantité</th>
                                <th>Réservé</th>
                                <th>Disponible</th>
                                <th>Statut</th>
                                <th>Dernière MAJ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in stocks %}
                            <tr class="{% if stock.is_low_stock %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ stock.warehouse.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ stock.warehouse.code }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{% if stock.warehouse.warehouse_type == 'central' %}primary{% else %}info{% endif %}">
                                        {{ stock.warehouse.get_warehouse_type_display }}
                                    </span>
                                </td>
                                <td>
                                    <strong>{{ stock.quantity }}</strong> {{ product.unit }}
                                </td>
                                <td>
                                    {% if stock.reserved_quantity > 0 %}
                                        <span class="text-warning">{{ stock.reserved_quantity }} {{ product.unit }}</span>
                                    {% else %}
                                        <span class="text-muted">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="{% if stock.available_quantity <= 0 %}text-danger{% elif stock.is_low_stock %}text-warning{% else %}text-success{% endif %}">
                                        {{ stock.available_quantity }} {{ product.unit }}
                                    </strong>
                                </td>
                                <td>
                                    {% if stock.quantity <= 0 %}
                                        <span class="badge bg-danger">Rupture</span>
                                    {% elif stock.is_low_stock %}
                                        <span class="badge bg-warning">Stock faible</span>
                                    {% else %}
                                        <span class="badge bg-success">OK</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ stock.last_updated|date:"d/m/Y H:i" }}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-graph-up display-4 text-muted"></i>
                    <h5 class="mt-3">Aucun stock trouvé</h5>
                    <p class="text-muted">Ce produit n'est présent dans aucun magasin.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Historique des mouvements -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Historique récent</h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <h5 class="mt-3">Historique non disponible</h5>
                    <p class="text-muted mb-0">L'historique des mouvements sera affiché ici.</p>
                    <small class="text-muted">Cette fonctionnalité sera ajoutée dans une future version</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Résumé du produit -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clipboard-data"></i> Résumé</h6>
            </div>
            <div class="card-body text-center">
                <div class="product-icon mx-auto mb-3">
                    <i class="bi bi-box-seam"></i>
                </div>
                <h5>{{ product.name }}</h5>
                <p class="text-muted">{{ product.reference }}</p>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="{% if total_stock <= product.minimum_stock %}text-warning{% else %}text-success{% endif %}">
                            {{ total_stock }}
                        </h6>
                        <small class="text-muted">Stock total</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">{{ stocks|length }}</h6>
                        <small class="text-muted">Magasins</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertes -->
        {% if total_stock <= product.minimum_stock %}
        <div class="card mb-4 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Alerte stock</h6>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>Stock faible détecté !</strong>
                </p>
                <p class="small text-muted mb-3">
                    Le stock total ({{ total_stock }} {{ product.unit }}) est inférieur ou égal au minimum requis ({{ product.minimum_stock }} {{ product.unit }}).
                </p>
                {% if user.can_manage_central_warehouse %}
                <a href="#" class="btn btn-warning btn-sm">
                    <i class="bi bi-cart-plus"></i> Réapprovisionner
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:product_edit' product.pk %}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> Modifier le produit
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'inventory:stock_list' %}?search={{ product.name }}" class="btn btn-outline-info">
                        <i class="bi bi-graph-up"></i> Voir tous les stocks
                    </a>
                    
                    {% if user.can_manage_central_warehouse %}
                    <a href="#" class="btn btn-outline-success">
                        <i class="bi bi-plus-circle"></i> Ajuster le stock
                    </a>
                    
                    <a href="#" class="btn btn-outline-warning">
                        <i class="bi bi-cart-plus"></i> Commander
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.product-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}
</style>
{% endblock %}
