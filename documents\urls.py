from django.urls import path
from . import views

app_name = 'documents'

urlpatterns = [
    path('receptions/', views.reception_list, name='reception_list'),
    path('receptions/create/', views.reception_create, name='reception_create'),
    path('receptions/<int:pk>/', views.reception_detail, name='reception_detail'),
    path('receptions/<int:pk>/validate/', views.reception_validate, name='reception_validate'),
    
    path('documents/', views.document_list, name='document_list'),
    path('documents/upload/', views.document_upload, name='document_upload'),
    path('documents/<uuid:pk>/', views.document_detail, name='document_detail'),
    path('documents/<uuid:pk>/download/', views.document_download, name='document_download'),
    
    path('stock-movements/', views.stock_movement_list, name='stock_movement_list'),
]
