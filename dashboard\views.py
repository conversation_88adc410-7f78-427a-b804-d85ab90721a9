from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from accounts.decorators import manager_or_admin_required
from inventory.models import Product, Stock, Warehouse
from procurement.models import PurchaseOrder
from distribution.models import InternalOrder
from documents.models import StockMovement


@login_required
def home_view(request):
    """Tableau de bord principal"""
    user = request.user
    context = {
        'user': user,
    }

    # Statistiques générales pour admin/manager
    if user.can_view_all_data():
        context.update({
            'total_products': Product.objects.filter(is_active=True).count(),
            'total_warehouses': Warehouse.objects.filter(is_active=True).count(),
            'low_stock_count': Stock.objects.filter(
                quantity__lte=F('product__minimum_stock')
            ).count(),
            'pending_orders': InternalOrder.objects.filter(
                status__in=['draft', 'pending_approval']
            ).count(),
            'recent_movements': StockMovement.objects.select_related(
                'product', 'warehouse'
            ).order_by('-created_at')[:10],
        })

    # Statistiques pour magasinier central
    elif user.can_manage_central_warehouse():
        central_warehouses = Warehouse.objects.filter(
            warehouse_type='central',
            is_active=True
        )
        if user.role == 'central_storekeeper':
            central_warehouses = central_warehouses.filter(manager=user)

        context.update({
            'my_warehouses': central_warehouses,
            'pending_receptions': PurchaseOrder.objects.filter(
                status__in=['sent', 'confirmed', 'partially_received']
            ).count(),
            'pending_internal_orders': InternalOrder.objects.filter(
                status='approved'
            ).count(),
        })

    # Statistiques pour gérant de sous-magasin
    elif user.can_manage_sub_warehouse():
        my_warehouses = Warehouse.objects.filter(
            manager=user,
            warehouse_type='sub',
            is_active=True
        )
        context.update({
            'my_warehouses': my_warehouses,
            'my_stock_count': Stock.objects.filter(
                warehouse__in=my_warehouses
            ).count(),
            'my_low_stock': Stock.objects.filter(
                warehouse__in=my_warehouses,
                quantity__lte=F('product__minimum_stock')
            ).count(),
        })

    # Statistiques pour service demandeur
    else:
        context.update({
            'my_orders': InternalOrder.objects.filter(
                requested_by=user
            ).order_by('-created_at')[:5],
            'pending_orders_count': InternalOrder.objects.filter(
                requested_by=user,
                status__in=['draft', 'pending_approval', 'approved']
            ).count(),
        })

    return render(request, 'dashboard/home.html', context)


@manager_or_admin_required
def statistics_view(request):
    """Vue des statistiques avancées"""
    # Statistiques des 30 derniers jours
    thirty_days_ago = timezone.now() - timedelta(days=30)

    # Mouvements de stock par type
    movement_stats = StockMovement.objects.filter(
        created_at__gte=thirty_days_ago
    ).values('movement_type').annotate(
        count=Count('id'),
        total_quantity=Sum('quantity')
    )

    # Commandes internes par statut
    order_stats = InternalOrder.objects.values('status').annotate(
        count=Count('id')
    )

    # Produits les plus demandés
    top_products = StockMovement.objects.filter(
        movement_type='distribution',
        created_at__gte=thirty_days_ago
    ).values(
        'product__name'
    ).annotate(
        total_distributed=Sum('quantity')
    ).order_by('-total_distributed')[:10]

    context = {
        'movement_stats': movement_stats,
        'order_stats': order_stats,
        'top_products': top_products,
        'period': '30 derniers jours',
    }

    return render(request, 'dashboard/statistics.html', context)


@manager_or_admin_required
def reports_view(request):
    """Vue des rapports"""
    return render(request, 'dashboard/reports.html')
