from django.urls import path
from . import views

app_name = 'distribution'

urlpatterns = [
    path('services/', views.service_list, name='service_list'),
    path('services/create/', views.service_create, name='service_create'),
    
    path('internal-orders/', views.internal_order_list, name='internal_order_list'),
    path('internal-orders/create/', views.internal_order_create, name='internal_order_create'),
    path('internal-orders/<int:pk>/', views.internal_order_detail, name='internal_order_detail'),
    path('internal-orders/<int:pk>/edit/', views.internal_order_edit, name='internal_order_edit'),
    path('internal-orders/<int:pk>/approve/', views.internal_order_approve, name='internal_order_approve'),
    path('internal-orders/<int:pk>/reject/', views.internal_order_reject, name='internal_order_reject'),
    
    path('manage-orders/', views.internal_order_manage, name='internal_order_manage'),
    
    path('delivery-notes/', views.delivery_note_list, name='delivery_note_list'),
    path('delivery-notes/create/<int:order_id>/', views.delivery_note_create, name='delivery_note_create'),
    path('delivery-notes/<int:pk>/', views.delivery_note_detail, name='delivery_note_detail'),
]
