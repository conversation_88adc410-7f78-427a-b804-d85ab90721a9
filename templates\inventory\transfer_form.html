{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Modifier{% else %}Nouveau{% endif %} transfert - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:transfer_list' %}">Transferts</a></li>
                {% if form.instance.pk %}
                <li class="breadcrumb-item"><a href="{% url 'inventory:transfer_detail' form.instance.pk %}">{{ form.instance.number }}</a></li>
                <li class="breadcrumb-item active">Modifier</li>
                {% else %}
                <li class="breadcrumb-item active">Nouveau transfert</li>
                {% endif %}
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-arrow-left-right"></i>
            {% if form.instance.pk %}
                Modifier le transfert
            {% else %}
                Nouveau transfert de stock
            {% endif %}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <form method="post" novalidate id="transfer-form">
            {% csrf_token %}
            
            <!-- Informations du transfert -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations du transfert</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.from_warehouse.id_for_label }}" class="form-label">
                                    {{ form.from_warehouse.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.from_warehouse }}
                                {% if form.from_warehouse.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.from_warehouse.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.to_warehouse.id_for_label }}" class="form-label">
                                    {{ form.to_warehouse.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.to_warehouse }}
                                {% if form.to_warehouse.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.to_warehouse.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.transfer_date.id_for_label }}" class="form-label">
                                    {{ form.transfer_date.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.transfer_date }}
                                {% if form.transfer_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.transfer_date.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Notes ou commentaires sur ce transfert
                        </small>
                    </div>
                </div>
            </div>

            <!-- Lignes de transfert -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> Produits à transférer</h5>
                    <button type="button" class="btn btn-sm btn-success" id="add-line">
                        <i class="bi bi-plus-circle"></i> Ajouter un produit
                    </button>
                </div>
                <div class="card-body">
                    <div id="transfer-lines">
                        {{ formset.management_form }}
                        {% for form in formset %}
                        <div class="transfer-line border rounded p-3 mb-3" data-form-index="{{ forloop.counter0 }}">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label class="form-label">Produit <span class="text-danger">*</span></label>
                                        {{ form.product }}
                                        {% if form.product.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.product.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Quantité <span class="text-danger">*</span></label>
                                        {{ form.quantity_requested }}
                                        {% if form.quantity_requested.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.quantity_requested.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Stock disponible</label>
                                        <input type="text" class="form-control stock-available" readonly placeholder="Sélectionnez un produit">
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm remove-line d-block">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {{ form.id }}
                            {% if form.DELETE %}
                                {{ form.DELETE }}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div id="empty-form" style="display: none;">
                        <div class="transfer-line border rounded p-3 mb-3" data-form-index="__prefix__">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="mb-3">
                                        <label class="form-label">Produit <span class="text-danger">*</span></label>
                                        {{ formset.empty_form.product }}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Quantité <span class="text-danger">*</span></label>
                                        {{ formset.empty_form.quantity_requested }}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Stock disponible</label>
                                        <input type="text" class="form-control stock-available" readonly placeholder="Sélectionnez un produit">
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm remove-line d-block">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {{ formset.empty_form.id }}
                            {{ formset.empty_form.DELETE }}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Erreurs générales du formulaire -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
            
            {% if formset.non_form_errors %}
            <div class="alert alert-danger">
                {{ formset.non_form_errors }}
            </div>
            {% endif %}
            
            <div class="d-flex justify-content-between">
                <a href="{% if form.instance.pk %}{% url 'inventory:transfer_detail' form.instance.pk %}{% else %}{% url 'inventory:transfer_list' %}{% endif %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Annuler
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i> 
                    {% if form.instance.pk %}Modifier{% else %}Créer{% endif %} le transfert
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let formIndex = {{ formset.total_form_count }};
    const addButton = document.getElementById('add-line');
    const transferLines = document.getElementById('transfer-lines');
    const emptyForm = document.getElementById('empty-form');
    const totalFormsInput = document.getElementById('id_lines-TOTAL_FORMS');
    
    // Ajouter une nouvelle ligne
    addButton.addEventListener('click', function() {
        const newForm = emptyForm.innerHTML.replace(/__prefix__/g, formIndex);
        const newDiv = document.createElement('div');
        newDiv.innerHTML = newForm;
        transferLines.appendChild(newDiv.firstElementChild);
        
        formIndex++;
        totalFormsInput.value = formIndex;
        
        // Réattacher les événements
        attachEvents();
    });
    
    // Supprimer une ligne
    function attachEvents() {
        document.querySelectorAll('.remove-line').forEach(button => {
            button.addEventListener('click', function() {
                const line = this.closest('.transfer-line');
                const deleteInput = line.querySelector('input[name$="-DELETE"]');
                if (deleteInput) {
                    deleteInput.checked = true;
                    line.style.display = 'none';
                } else {
                    line.remove();
                    formIndex--;
                    totalFormsInput.value = formIndex;
                }
            });
        });
        
        // Gérer le changement de magasin source
        document.getElementById('{{ form.from_warehouse.id_for_label }}').addEventListener('change', function() {
            updateAvailableStock();
        });
        
        // Gérer le changement de produit
        document.querySelectorAll('select[name$="-product"]').forEach(select => {
            select.addEventListener('change', function() {
                updateStockForLine(this);
            });
        });
    }
    
    // Mettre à jour le stock disponible pour une ligne
    function updateStockForLine(productSelect) {
        const line = productSelect.closest('.transfer-line');
        const stockInput = line.querySelector('.stock-available');
        const warehouseId = document.getElementById('{{ form.from_warehouse.id_for_label }}').value;
        const productId = productSelect.value;
        
        if (warehouseId && productId) {
            // Ici vous pourriez faire un appel AJAX pour récupérer le stock
            // Pour l'instant, on affiche un placeholder
            stockInput.value = 'Chargement...';
            
            // Simulation d'un appel AJAX
            setTimeout(() => {
                stockInput.value = 'Stock: 100 unités'; // Remplacer par la vraie valeur
            }, 500);
        } else {
            stockInput.value = '';
        }
    }
    
    // Mettre à jour tous les stocks disponibles
    function updateAvailableStock() {
        document.querySelectorAll('select[name$="-product"]').forEach(select => {
            if (select.value) {
                updateStockForLine(select);
            }
        });
    }
    
    // Initialiser les événements
    attachEvents();
    
    // Validation côté client
    document.getElementById('transfer-form').addEventListener('submit', function(e) {
        const fromWarehouse = document.getElementById('{{ form.from_warehouse.id_for_label }}').value;
        const toWarehouse = document.getElementById('{{ form.to_warehouse.id_for_label }}').value;
        
        if (fromWarehouse === toWarehouse) {
            e.preventDefault();
            alert('Le magasin source et le magasin destination ne peuvent pas être identiques.');
            return false;
        }
        
        // Vérifier qu'il y a au moins une ligne
        const visibleLines = document.querySelectorAll('.transfer-line:not([style*="display: none"])');
        if (visibleLines.length === 0) {
            e.preventDefault();
            alert('Vous devez ajouter au moins un produit à transférer.');
            return false;
        }
    });
});
</script>
{% endblock %}
