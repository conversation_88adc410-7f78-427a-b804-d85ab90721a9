{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if form.instance.pk %}Modifier{% else %}Nouveau{% endif %} fournisseur - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'procurement:supplier_list' %}">Fournisseurs</a></li>
                <li class="breadcrumb-item active">
                    {% if form.instance.pk %}Modifier{% else %}Nouveau{% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-building"></i>
            {% if form.instance.pk %}
                Modifier le fournisseur
            {% else %}
                Nouveau fournisseur
            {% endif %}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Informations du fournisseur
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    {{ form.code.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.code.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.contact_person.id_for_label }}" class="form-label">
                                    {{ form.contact_person.label }}
                                </label>
                                {{ form.contact_person }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    {{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'procurement:supplier_list' %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            {% if form.instance.pk %}Modifier{% else %}Créer{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    <strong>Code fournisseur :</strong> Identifiant unique pour ce fournisseur.
                </p>
                <p class="small text-muted">
                    <strong>Personne de contact :</strong> Nom de la personne à contacter chez ce fournisseur.
                </p>
                <p class="small text-muted">
                    <strong>Statut :</strong> Décochez pour désactiver temporairement ce fournisseur.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
