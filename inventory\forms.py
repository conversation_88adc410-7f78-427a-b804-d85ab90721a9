from django import forms
from django.forms import inlineformset_factory
from .models import Product, ProductCategory, Warehouse, Stock, StockTransfer, StockTransferLine


class ProductForm(forms.ModelForm):
    """Formulaire pour les produits"""
    class Meta:
        model = Product
        fields = ['reference', 'name', 'category', 'description', 'unit', 'minimum_stock', 'is_active']
        widgets = {
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'unit': forms.TextInput(attrs={'class': 'form-control'}),
            'minimum_stock': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class ProductCategoryForm(forms.ModelForm):
    """Formulaire pour les catégories de produits"""
    class Meta:
        model = ProductCategory
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class WarehouseForm(forms.ModelForm):
    """Formulaire pour les magasins"""
    class Meta:
        model = Warehouse
        fields = ['code', 'name', 'warehouse_type', 'parent_warehouse', 'manager', 'address', 'is_active']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'warehouse_type': forms.Select(attrs={'class': 'form-control'}),
            'parent_warehouse': forms.Select(attrs={'class': 'form-control'}),
            'manager': forms.Select(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les magasins parents (seulement les centraux)
        self.fields['parent_warehouse'].queryset = Warehouse.objects.filter(
            warehouse_type='central',
            is_active=True
        )
        self.fields['parent_warehouse'].required = False
        
        # Filtrer les managers selon leur rôle
        from accounts.models import User
        self.fields['manager'].queryset = User.objects.filter(
            role__in=['central_storekeeper', 'sub_storekeeper'],
            is_active=True
        )


class StockTransferForm(forms.ModelForm):
    """Formulaire pour les transferts de stock"""
    class Meta:
        model = StockTransfer
        fields = ['from_warehouse', 'to_warehouse', 'transfer_date', 'notes']
        widgets = {
            'from_warehouse': forms.Select(attrs={'class': 'form-control'}),
            'to_warehouse': forms.Select(attrs={'class': 'form-control'}),
            'transfer_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filtrer les magasins selon les permissions de l'utilisateur
            if user.role == 'central_storekeeper':
                # Peut transférer depuis le central vers les sous-magasins
                self.fields['from_warehouse'].queryset = Warehouse.objects.filter(
                    warehouse_type='central',
                    is_active=True
                )
                self.fields['to_warehouse'].queryset = Warehouse.objects.filter(
                    warehouse_type='sub',
                    is_active=True
                )
            elif user.role == 'sub_storekeeper':
                # Peut transférer depuis son sous-magasin vers le central
                self.fields['from_warehouse'].queryset = Warehouse.objects.filter(
                    manager=user,
                    is_active=True
                )
                self.fields['to_warehouse'].queryset = Warehouse.objects.filter(
                    warehouse_type='central',
                    is_active=True
                )
            else:
                # Autres rôles : tous les magasins
                self.fields['from_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
                self.fields['to_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
    
    def clean(self):
        cleaned_data = super().clean()
        from_warehouse = cleaned_data.get('from_warehouse')
        to_warehouse = cleaned_data.get('to_warehouse')
        
        if from_warehouse and to_warehouse and from_warehouse == to_warehouse:
            raise forms.ValidationError("Le magasin source et le magasin destination doivent être différents.")
        
        return cleaned_data


class StockTransferLineForm(forms.ModelForm):
    """Formulaire pour les lignes de transfert"""
    class Meta:
        model = StockTransferLine
        fields = ['product', 'quantity_requested']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'quantity_requested': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
        }
    
    def __init__(self, *args, **kwargs):
        from_warehouse = kwargs.pop('from_warehouse', None)
        super().__init__(*args, **kwargs)
        
        if from_warehouse:
            # Limiter les produits à ceux disponibles dans le magasin source
            available_products = Stock.objects.filter(
                warehouse=from_warehouse,
                quantity__gt=0
            ).values_list('product_id', flat=True)
            
            self.fields['product'].queryset = Product.objects.filter(
                id__in=available_products,
                is_active=True
            )


# Formset pour les lignes de transfert
StockTransferLineFormSet = inlineformset_factory(
    StockTransfer,
    StockTransferLine,
    form=StockTransferLineForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class StockSearchForm(forms.Form):
    """Formulaire de recherche pour les stocks"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher un produit...'
        })
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        required=False,
        empty_label="Tous les magasins",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    category = forms.ModelChoiceField(
        queryset=ProductCategory.objects.all(),
        required=False,
        empty_label="Toutes les catégories",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    low_stock_only = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label="Stock faible uniquement"
    )


class StockTransferSearchForm(forms.Form):
    """Formulaire de recherche pour les transferts"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par numéro...'
        })
    )
    from_warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        required=False,
        empty_label="Tous les magasins source",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    to_warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        required=False,
        empty_label="Tous les magasins destination",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + StockTransfer.TRANSFER_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
