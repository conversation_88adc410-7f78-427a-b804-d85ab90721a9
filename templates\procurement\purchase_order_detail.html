{% extends 'base.html' %}

{% block title %}BC {{ order.number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'procurement:purchase_order_list' %}">Bons de commande</a></li>
                <li class="breadcrumb-item active">{{ order.number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-cart"></i> Bon de commande {{ order.number }}
                {% if order.status == 'draft' %}
                    <span class="badge bg-secondary ms-2">Brouillon</span>
                {% elif order.status == 'sent' %}
                    <span class="badge bg-primary ms-2">Envoyé</span>
                {% elif order.status == 'confirmed' %}
                    <span class="badge bg-info ms-2">Confirmé</span>
                {% elif order.status == 'partially_received' %}
                    <span class="badge bg-warning ms-2">Partiellement reçu</span>
                {% elif order.status == 'fully_received' %}
                    <span class="badge bg-success ms-2">Entièrement reçu</span>
                {% elif order.status == 'cancelled' %}
                    <span class="badge bg-danger ms-2">Annulé</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'procurement:purchase_order_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                {% if order.status == 'draft' %}
                <a href="{% url 'procurement:purchase_order_edit' order.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                {% endif %}
                {% if order.status in 'sent,confirmed,partially_received' %}
                <a href="{% url 'documents:reception_create' %}?purchase_order={{ order.pk }}" class="btn btn-success">
                    <i class="bi bi-truck"></i> Réceptionner
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations générales -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Numéro :</dt>
                            <dd class="col-sm-7"><strong>{{ order.number }}</strong></dd>
                            
                            <dt class="col-sm-5">Fournisseur :</dt>
                            <dd class="col-sm-7">
                                <a href="{% url 'procurement:supplier_detail' order.supplier.pk %}">
                                    {{ order.supplier.name }}
                                </a>
                            </dd>
                            
                            <dt class="col-sm-5">Contrat :</dt>
                            <dd class="col-sm-7">
                                {% if order.contract %}
                                    <a href="{% url 'procurement:contract_detail' order.contract.pk %}">
                                        {{ order.contract.number }}
                                    </a>
                                {% else %}
                                    -
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Date commande :</dt>
                            <dd class="col-sm-7">{{ order.order_date|date:"d/m/Y" }}</dd>
                            
                            <dt class="col-sm-5">Livraison prévue :</dt>
                            <dd class="col-sm-7">{{ order.expected_delivery_date|date:"d/m/Y"|default:"-" }}</dd>
                            
                            <dt class="col-sm-5">Créé par :</dt>
                            <dd class="col-sm-7">{{ order.created_by.get_full_name }}</dd>
                            
                            <dt class="col-sm-5">Créé le :</dt>
                            <dd class="col-sm-7">{{ order.created_at|date:"d/m/Y H:i" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if order.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes :</h6>
                        <p class="text-muted">{{ order.notes|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Lignes de commande -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list"></i> Produits commandés</h5>
            </div>
            <div class="card-body">
                {% if lines %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Qté commandée</th>
                                <th>Qté reçue</th>
                                <th>Qté restante</th>
                                <th>Prix unitaire</th>
                                <th>Total ligne</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in lines %}
                            <tr>
                                <td>
                                    <strong>{{ line.product.reference }}</strong><br>
                                    <small class="text-muted">{{ line.product.name }}</small>
                                </td>
                                <td>{{ line.quantity_ordered }} {{ line.product.unit }}</td>
                                <td>
                                    <span class="badge bg-{% if line.quantity_received == 0 %}secondary{% elif line.is_fully_received %}success{% else %}warning{% endif %}">
                                        {{ line.quantity_received }} {{ line.product.unit }}
                                    </span>
                                </td>
                                <td>
                                    {% if line.quantity_remaining > 0 %}
                                        <span class="badge bg-info">{{ line.quantity_remaining }} {{ line.product.unit }}</span>
                                    {% else %}
                                        <span class="badge bg-success">Complet</span>
                                    {% endif %}
                                </td>
                                <td>{{ line.unit_price|floatformat:2 }} €</td>
                                <td><strong>{{ line.line_total|floatformat:2 }} €</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-info">
                                <th colspan="5" class="text-end">Total général :</th>
                                <th>{{ order.total_amount|floatformat:2 }} €</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center py-3">Aucun produit dans ce bon de commande.</p>
                {% endif %}
            </div>
        </div>

        <!-- Réceptions associées -->
        {% if order.reception_set.all %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-truck"></i> Réceptions</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Date</th>
                                <th>Magasin</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for reception in order.reception_set.all %}
                            <tr>
                                <td>
                                    <a href="{% url 'documents:reception_detail' reception.pk %}">
                                        {{ reception.number }}
                                    </a>
                                </td>
                                <td>{{ reception.reception_date|date:"d/m/Y" }}</td>
                                <td>{{ reception.warehouse.name }}</td>
                                <td>
                                    {% if reception.status == 'draft' %}
                                        <span class="badge bg-warning">Brouillon</span>
                                    {% elif reception.status == 'validated' %}
                                        <span class="badge bg-success">Validée</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'documents:reception_detail' reception.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Résumé -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-graph-up"></i> Résumé</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ lines.count }}</h4>
                        <small class="text-muted">Produits</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ order.total_amount|floatformat:2 }} €</h4>
                        <small class="text-muted">Montant total</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-info">{{ order.reception_set.count }}</h5>
                        <small class="text-muted">Réceptions</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning">
                            {% widthratio order.lines.all|length order.lines.all|length 100 %}%
                        </h5>
                        <small class="text-muted">Progression</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if order.status == 'draft' %}
                    <a href="{% url 'procurement:purchase_order_edit' order.pk %}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil"></i> Modifier
                    </a>
                    {% endif %}
                    
                    {% if order.status in 'sent,confirmed,partially_received' %}
                    <a href="{% url 'documents:reception_create' %}?purchase_order={{ order.pk }}" class="btn btn-outline-success">
                        <i class="bi bi-truck"></i> Nouvelle réception
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'procurement:supplier_detail' order.supplier.pk %}" class="btn btn-outline-info">
                        <i class="bi bi-building"></i> Voir fournisseur
                    </a>
                    
                    {% if order.contract %}
                    <a href="{% url 'procurement:contract_detail' order.contract.pk %}" class="btn btn-outline-secondary">
                        <i class="bi bi-file-text"></i> Voir contrat
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
