from django.contrib import admin
from .models import InternalService, InternalOrder, InternalOrderLine, DeliveryNote


@admin.register(InternalService)
class InternalServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'manager', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')
    ordering = ('name',)


class InternalOrderLineInline(admin.TabularInline):
    model = InternalOrderLine
    extra = 1


@admin.register(InternalOrder)
class InternalOrderAdmin(admin.ModelAdmin):
    list_display = ('number', 'requesting_service', 'requested_by', 'request_date', 'priority', 'status')
    list_filter = ('status', 'priority', 'requesting_service')
    search_fields = ('number',)
    ordering = ('-created_at',)
    inlines = [InternalOrderLineInline]


@admin.register(DeliveryNote)
class DeliveryNoteAdmin(admin.ModelAdmin):
    list_display = ('number', 'internal_order', 'delivery_date', 'delivered_by', 'received_by_name', 'status')
    list_filter = ('status',)
    search_fields = ('number',)
    ordering = ('-created_at',)
