from django.db import models
from django.contrib.auth import get_user_model
from inventory.models import Product, Warehouse, Stock
from django.core.validators import MinValueValidator

User = get_user_model()


class InternalService(models.Model):
    """Service interne demandeur"""
    name = models.CharField(max_length=100, verbose_name="Nom du service")
    code = models.CharField(max_length=20, unique=True, verbose_name="Code service")
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Responsable"
    )
    description = models.TextField(blank=True, verbose_name="Description")
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Service interne"
        verbose_name_plural = "Services internes"
        ordering = ['name']

    def __str__(self):
        return self.name


class InternalOrder(models.Model):
    """Bon de commande interne (BCN)"""
    ORDER_STATUS = [
        ('draft', 'Brouillon'),
        ('pending_approval', 'En attente d\'approbation'),
        ('approved', 'Approuvé'),
        ('partially_delivered', 'Partiellement livré'),
        ('fully_delivered', 'Entièrement livré'),
        ('cancelled', 'Annulé'),
        ('rejected', 'Rejeté'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Basse'),
        ('normal', 'Normale'),
        ('high', 'Haute'),
        ('urgent', 'Urgente'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro BCN")
    requesting_service = models.ForeignKey(
        InternalService,
        on_delete=models.PROTECT,
        verbose_name="Service demandeur"
    )
    requested_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='requested_orders',
        verbose_name="Demandé par"
    )
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        verbose_name="Magasin de livraison"
    )
    request_date = models.DateField(verbose_name="Date de demande")
    needed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Date de besoin"
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name="Priorité"
    )
    status = models.CharField(
        max_length=20,
        choices=ORDER_STATUS,
        default='draft',
        verbose_name="Statut"
    )
    justification = models.TextField(verbose_name="Justification de la demande")
    notes = models.TextField(blank=True, verbose_name="Notes")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_orders',
        verbose_name="Approuvé par"
    )
    approval_date = models.DateTimeField(null=True, blank=True, verbose_name="Date d'approbation")
    rejection_reason = models.TextField(blank=True, verbose_name="Motif de rejet")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Bon de commande interne"
        verbose_name_plural = "Bons de commande internes"
        ordering = ['-created_at']

    def __str__(self):
        return f"BCN {self.number} - {self.requesting_service.name}"


class InternalOrderLine(models.Model):
    """Ligne de bon de commande interne"""
    internal_order = models.ForeignKey(
        InternalOrder,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name="BCN"
    )
    product = models.ForeignKey(Product, on_delete=models.PROTECT, verbose_name="Produit")
    quantity_requested = models.PositiveIntegerField(verbose_name="Quantité demandée")
    quantity_approved = models.PositiveIntegerField(default=0, verbose_name="Quantité approuvée")
    quantity_delivered = models.PositiveIntegerField(default=0, verbose_name="Quantité livrée")
    justification = models.TextField(blank=True, verbose_name="Justification")

    class Meta:
        verbose_name = "Ligne BCN"
        verbose_name_plural = "Lignes BCN"
        unique_together = ['internal_order', 'product']

    def __str__(self):
        return f"{self.product.name} - {self.quantity_requested} {self.product.unit}"

    @property
    def quantity_remaining(self):
        """Quantité restante à livrer"""
        return self.quantity_approved - self.quantity_delivered

    @property
    def is_fully_delivered(self):
        """Vérifie si la ligne est entièrement livrée"""
        return self.quantity_delivered >= self.quantity_approved


class DeliveryNote(models.Model):
    """Bon de livraison (BDL)"""
    DELIVERY_STATUS = [
        ('draft', 'Brouillon'),
        ('delivered', 'Livré'),
        ('partially_delivered', 'Partiellement livré'),
        ('cancelled', 'Annulé'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro BDL")
    internal_order = models.ForeignKey(
        InternalOrder,
        on_delete=models.PROTECT,
        verbose_name="BCN associé"
    )
    delivery_date = models.DateField(verbose_name="Date de livraison")
    delivered_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='delivered_orders',
        verbose_name="Livré par"
    )
    received_by_name = models.CharField(max_length=100, verbose_name="Reçu par (nom)")
    received_by_signature = models.TextField(blank=True, verbose_name="Signature (base64)")
    status = models.CharField(
        max_length=20,
        choices=DELIVERY_STATUS,
        default='draft',
        verbose_name="Statut"
    )
    notes = models.TextField(blank=True, verbose_name="Notes")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Bon de livraison"
        verbose_name_plural = "Bons de livraison"
        ordering = ['-created_at']

    def __str__(self):
        return f"BDL {self.number} - {self.internal_order.requesting_service.name}"
