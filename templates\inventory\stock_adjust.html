{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Ajustement de stock - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:stock_list' %}">Stocks</a></li>
                <li class="breadcrumb-item active">Ajustement de stock</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-gear"></i> Ajustement de stock
        </h1>
        <p class="text-muted">Ajustez les quantités en stock pour corriger les écarts d'inventaire.</p>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <form method="post" novalidate id="stock-adjust-form">
            {% csrf_token %}
            
            <!-- Sélection du produit et magasin -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-search"></i> Sélection</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="warehouse" class="form-label">
                                    Magasin <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="warehouse" name="warehouse" required>
                                    <option value="">Sélectionnez un magasin</option>
                                    <!-- Options will be populated dynamically -->
                                </select>
                                <small class="form-text text-muted">
                                    Choisissez le magasin où ajuster le stock
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product" class="form-label">
                                    Produit <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="product" name="product" required>
                                    <option value="">Sélectionnez d'abord un magasin</option>
                                </select>
                                <small class="form-text text-muted">
                                    Produit à ajuster
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations actuelles -->
            <div class="card mb-4" id="current-stock-card" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Stock actuel</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-primary mb-1" id="current-quantity">-</h4>
                                <small class="text-muted">Quantité actuelle</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-warning mb-1" id="minimum-stock">-</h4>
                                <small class="text-muted">Stock minimum</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-info mb-1" id="product-unit">-</h4>
                                <small class="text-muted">Unité</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ajustement -->
            <div class="card mb-4" id="adjustment-card" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-gear"></i> Ajustement</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="adjustment_type" class="form-label">
                                    Type d'ajustement <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" id="adjustment_type" name="adjustment_type" required>
                                    <option value="">Sélectionnez le type</option>
                                    <option value="increase">Augmentation (+)</option>
                                    <option value="decrease">Diminution (-)</option>
                                    <option value="set">Définir la quantité exacte</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    Quantité <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="0" step="0.01" required>
                                <small class="form-text text-muted" id="quantity-help">
                                    Entrez la quantité à ajuster
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">
                            Motif de l'ajustement <span class="text-danger">*</span>
                        </label>
                        <select class="form-control" id="reason" name="reason" required>
                            <option value="">Sélectionnez un motif</option>
                            <option value="inventory_correction">Correction d'inventaire</option>
                            <option value="damaged_goods">Produits endommagés</option>
                            <option value="expired_goods">Produits expirés</option>
                            <option value="theft_loss">Perte/Vol</option>
                            <option value="supplier_error">Erreur fournisseur</option>
                            <option value="system_error">Erreur système</option>
                            <option value="other">Autre</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Détails supplémentaires sur l'ajustement..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Aperçu de l'ajustement -->
            <div class="card mb-4" id="preview-card" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-eye"></i> Aperçu</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h5 class="text-muted mb-1">Avant</h5>
                                <h4 class="text-secondary" id="preview-before">-</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <i class="bi bi-arrow-right display-4 text-muted"></i>
                                <div class="mt-2">
                                    <span class="badge bg-info" id="preview-operation">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h5 class="text-muted mb-1">Après</h5>
                                <h4 id="preview-after" class="text-success">-</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'inventory:stock_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Annuler
                </a>
                <button type="submit" class="btn btn-warning" id="submit-btn" disabled>
                    <i class="bi bi-gear"></i> Effectuer l'ajustement
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <h6>Types d'ajustement :</h6>
                <ul class="small">
                    <li><strong>Augmentation :</strong> Ajoute la quantité au stock actuel</li>
                    <li><strong>Diminution :</strong> Soustrait la quantité du stock actuel</li>
                    <li><strong>Définir :</strong> Remplace le stock par la quantité exacte</li>
                </ul>
                
                <hr>
                
                <h6>Motifs courants :</h6>
                <ul class="small text-muted">
                    <li>Correction d'inventaire physique</li>
                    <li>Produits endommagés ou expirés</li>
                    <li>Erreurs de saisie ou système</li>
                    <li>Pertes ou vols constatés</li>
                </ul>
                
                <hr>
                
                <div class="alert alert-warning small">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    <strong>Attention :</strong> Les ajustements de stock sont définitifs et tracés dans l'historique.
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clock-history"></i> Derniers ajustements</h6>
            </div>
            <div class="card-body">
                <div class="text-center py-3">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <h6 class="mt-3">Historique</h6>
                    <p class="text-muted small mb-0">Les derniers ajustements s'afficheront ici.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const warehouseSelect = document.getElementById('warehouse');
    const productSelect = document.getElementById('product');
    const adjustmentTypeSelect = document.getElementById('adjustment_type');
    const quantityInput = document.getElementById('quantity');
    const currentStockCard = document.getElementById('current-stock-card');
    const adjustmentCard = document.getElementById('adjustment-card');
    const previewCard = document.getElementById('preview-card');
    const submitBtn = document.getElementById('submit-btn');
    
    let currentStock = 0;
    let minimumStock = 0;
    let productUnit = '';
    
    // Simuler le chargement des magasins
    // En réalité, ceci devrait être fait via un appel AJAX ou rendu côté serveur
    const warehouses = [
        {id: 1, name: 'Magasin Central'},
        {id: 2, name: 'Sous-magasin A'},
        {id: 3, name: 'Sous-magasin B'}
    ];
    
    warehouses.forEach(warehouse => {
        const option = document.createElement('option');
        option.value = warehouse.id;
        option.textContent = warehouse.name;
        warehouseSelect.appendChild(option);
    });
    
    // Gérer le changement de magasin
    warehouseSelect.addEventListener('change', function() {
        productSelect.innerHTML = '<option value="">Chargement...</option>';
        currentStockCard.style.display = 'none';
        adjustmentCard.style.display = 'none';
        previewCard.style.display = 'none';
        
        if (this.value) {
            // Simuler le chargement des produits
            setTimeout(() => {
                productSelect.innerHTML = '<option value="">Sélectionnez un produit</option>';
                const products = [
                    {id: 1, name: 'Détergent A', reference: 'DET001'},
                    {id: 2, name: 'Savon B', reference: 'SAV002'},
                    {id: 3, name: 'Désinfectant C', reference: 'DES003'}
                ];
                
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = `${product.reference} - ${product.name}`;
                    productSelect.appendChild(option);
                });
            }, 500);
        } else {
            productSelect.innerHTML = '<option value="">Sélectionnez d\'abord un magasin</option>';
        }
    });
    
    // Gérer le changement de produit
    productSelect.addEventListener('change', function() {
        if (this.value) {
            // Simuler le chargement des informations de stock
            currentStock = Math.floor(Math.random() * 100) + 10;
            minimumStock = Math.floor(Math.random() * 20) + 5;
            productUnit = 'litre';
            
            document.getElementById('current-quantity').textContent = currentStock;
            document.getElementById('minimum-stock').textContent = minimumStock;
            document.getElementById('product-unit').textContent = productUnit;
            
            currentStockCard.style.display = 'block';
            adjustmentCard.style.display = 'block';
        } else {
            currentStockCard.style.display = 'none';
            adjustmentCard.style.display = 'none';
            previewCard.style.display = 'none';
        }
        updatePreview();
    });
    
    // Gérer les changements d'ajustement
    [adjustmentTypeSelect, quantityInput].forEach(element => {
        element.addEventListener('change', updatePreview);
        element.addEventListener('input', updatePreview);
    });
    
    function updatePreview() {
        const type = adjustmentTypeSelect.value;
        const quantity = parseFloat(quantityInput.value) || 0;
        
        if (type && quantity > 0 && productSelect.value) {
            let newStock = currentStock;
            let operation = '';
            
            switch(type) {
                case 'increase':
                    newStock = currentStock + quantity;
                    operation = `+${quantity}`;
                    break;
                case 'decrease':
                    newStock = Math.max(0, currentStock - quantity);
                    operation = `-${quantity}`;
                    break;
                case 'set':
                    newStock = quantity;
                    operation = `= ${quantity}`;
                    break;
            }
            
            document.getElementById('preview-before').textContent = currentStock;
            document.getElementById('preview-operation').textContent = operation;
            document.getElementById('preview-after').textContent = newStock;
            document.getElementById('preview-after').className = newStock < minimumStock ? 'text-warning' : 'text-success';
            
            previewCard.style.display = 'block';
            submitBtn.disabled = false;
        } else {
            previewCard.style.display = 'none';
            submitBtn.disabled = true;
        }
    }
    
    // Mettre à jour l'aide selon le type
    adjustmentTypeSelect.addEventListener('change', function() {
        const help = document.getElementById('quantity-help');
        switch(this.value) {
            case 'increase':
                help.textContent = 'Quantité à ajouter au stock actuel';
                break;
            case 'decrease':
                help.textContent = 'Quantité à retirer du stock actuel';
                break;
            case 'set':
                help.textContent = 'Nouvelle quantité exacte en stock';
                break;
            default:
                help.textContent = 'Entrez la quantité à ajuster';
        }
    });
});
</script>
{% endblock %}
