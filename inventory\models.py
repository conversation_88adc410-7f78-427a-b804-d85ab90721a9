from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal

User = get_user_model()


class ProductCategory(models.Model):
    """Catégorie de produits de nettoyage"""
    name = models.CharField(max_length=100, unique=True, verbose_name="Nom")
    description = models.TextField(blank=True, verbose_name="Description")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Catégorie de produit"
        verbose_name_plural = "Catégories de produits"
        ordering = ['name']

    def __str__(self):
        return self.name


class Product(models.Model):
    """Produit de nettoyage"""
    name = models.CharField(max_length=200, verbose_name="Nom du produit")
    reference = models.CharField(max_length=50, unique=True, verbose_name="Référence")
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.PROTECT,
        verbose_name="Catégorie"
    )
    description = models.TextField(blank=True, verbose_name="Description")
    unit = models.CharField(max_length=20, verbose_name="Unité", default="pièce")
    minimum_stock = models.PositiveIntegerField(
        default=0,
        verbose_name="Stock minimum"
    )
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Produit"
        verbose_name_plural = "Produits"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.reference})"


class Warehouse(models.Model):
    """Magasin/Entrepôt"""
    WAREHOUSE_TYPES = [
        ('central', 'Magasin Central'),
        ('sub', 'Sous-Magasin'),
    ]

    name = models.CharField(max_length=100, verbose_name="Nom du magasin")
    code = models.CharField(max_length=20, unique=True, verbose_name="Code")
    warehouse_type = models.CharField(
        max_length=10,
        choices=WAREHOUSE_TYPES,
        verbose_name="Type de magasin"
    )
    parent_warehouse = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="Magasin parent"
    )
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Gérant"
    )
    address = models.TextField(blank=True, verbose_name="Adresse")
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Magasin"
        verbose_name_plural = "Magasins"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_warehouse_type_display()})"


class Stock(models.Model):
    """Stock de produits par magasin"""
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        verbose_name="Magasin"
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name="Produit"
    )
    quantity = models.PositiveIntegerField(default=0, verbose_name="Quantité")
    reserved_quantity = models.PositiveIntegerField(
        default=0,
        verbose_name="Quantité réservée"
    )
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Stock"
        verbose_name_plural = "Stocks"
        unique_together = ['warehouse', 'product']
        ordering = ['warehouse', 'product']

    def __str__(self):
        return f"{self.product.name} - {self.warehouse.name}: {self.quantity}"

    @property
    def available_quantity(self):
        """Quantité disponible (non réservée)"""
        return self.quantity - self.reserved_quantity

    @property
    def is_low_stock(self):
        """Vérifie si le stock est en dessous du minimum"""
        return self.quantity <= self.product.minimum_stock


class StockTransfer(models.Model):
    """Transfert de stock entre magasins"""
    TRANSFER_STATUS = [
        ('draft', 'Brouillon'),
        ('pending', 'En attente'),
        ('in_transit', 'En transit'),
        ('completed', 'Terminé'),
        ('cancelled', 'Annulé'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro de transfert")
    from_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='outgoing_transfers',
        verbose_name="Magasin source"
    )
    to_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='incoming_transfers',
        verbose_name="Magasin destination"
    )
    transfer_date = models.DateField(verbose_name="Date de transfert")
    expected_arrival_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Date d'arrivée prévue"
    )
    actual_arrival_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Date d'arrivée réelle"
    )
    status = models.CharField(
        max_length=20,
        choices=TRANSFER_STATUS,
        default='draft',
        verbose_name="Statut"
    )
    notes = models.TextField(blank=True, verbose_name="Notes")
    requested_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='requested_transfers',
        verbose_name="Demandé par"
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transfers',
        verbose_name="Approuvé par"
    )
    sent_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_transfers',
        verbose_name="Expédié par"
    )
    received_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='received_transfers',
        verbose_name="Reçu par"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Transfert de stock"
        verbose_name_plural = "Transferts de stock"
        ordering = ['-created_at']

    def __str__(self):
        return f"Transfert {self.number} - {self.from_warehouse.name} → {self.to_warehouse.name}"


class StockTransferLine(models.Model):
    """Ligne de transfert de stock"""
    transfer = models.ForeignKey(
        StockTransfer,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name="Transfert"
    )
    product = models.ForeignKey(Product, on_delete=models.PROTECT, verbose_name="Produit")
    quantity_requested = models.PositiveIntegerField(verbose_name="Quantité demandée")
    quantity_sent = models.PositiveIntegerField(default=0, verbose_name="Quantité expédiée")
    quantity_received = models.PositiveIntegerField(default=0, verbose_name="Quantité reçue")
    notes = models.TextField(blank=True, verbose_name="Notes")

    class Meta:
        verbose_name = "Ligne de transfert"
        verbose_name_plural = "Lignes de transfert"
        unique_together = ['transfer', 'product']

    def __str__(self):
        return f"{self.product.name} - {self.quantity_requested} {self.product.unit}"

    @property
    def quantity_remaining_to_send(self):
        """Quantité restante à expédier"""
        return self.quantity_requested - self.quantity_sent

    @property
    def quantity_remaining_to_receive(self):
        """Quantité restante à recevoir"""
        return self.quantity_sent - self.quantity_received

    @property
    def is_fully_sent(self):
        """Vérifie si la ligne est entièrement expédiée"""
        return self.quantity_sent >= self.quantity_requested

    @property
    def is_fully_received(self):
        """Vérifie si la ligne est entièrement reçue"""
        return self.quantity_received >= self.quantity_sent
