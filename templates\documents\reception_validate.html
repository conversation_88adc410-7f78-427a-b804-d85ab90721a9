{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Validation réception {{ reception.number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'documents:reception_list' %}">Réceptions</a></li>
                <li class="breadcrumb-item"><a href="{% url 'documents:reception_detail' reception.pk %}">{{ reception.number }}</a></li>
                <li class="breadcrumb-item active">Validation</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-check-circle"></i> Validation réception {{ reception.number }}</h1>
            <span class="badge bg-warning fs-6">{{ reception.get_status_display }}</span>
        </div>
    </div>
</div>

<!-- Informations de la réception -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations de la réception</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Bon de commande :</strong><br>
                        <a href="{% url 'procurement:purchase_order_detail' reception.purchase_order.pk %}">
                            {{ reception.purchase_order.number }}
                        </a>
                    </div>
                    <div class="col-md-3">
                        <strong>Fournisseur :</strong><br>
                        {{ reception.purchase_order.supplier.name }}
                    </div>
                    <div class="col-md-3">
                        <strong>Magasin :</strong><br>
                        {{ reception.warehouse.name }}
                    </div>
                    <div class="col-md-3">
                        <strong>Date réception :</strong><br>
                        {{ reception.reception_date|date:"d/m/Y" }}
                    </div>
                </div>
                {% if reception.delivery_note_number %}
                <div class="row mt-2">
                    <div class="col-md-3">
                        <strong>BL n° :</strong><br>
                        {{ reception.delivery_note_number }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Formulaire de validation -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list-check"></i> Quantités reçues</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ formset.management_form }}
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Unité</th>
                                    <th>Qté commandée</th>
                                    <th>Qté déjà reçue</th>
                                    <th>Qté restante</th>
                                    <th>Qté à réceptionner <span class="text-danger">*</span></th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for form in formset %}
                                    {% for hidden in form.hidden_fields %}
                                        {{ hidden }}
                                    {% endfor %}
                                    
                                    <tr>
                                        <td>
                                            <strong>{{ form.instance.product.reference }}</strong><br>
                                            <small class="text-muted">{{ form.instance.product.name }}</small>
                                            {{ form.product.as_hidden }}
                                        </td>
                                        <td>{{ form.instance.product.unit }}</td>
                                        <td>
                                            {% for po_line in reception.purchase_order.lines.all %}
                                                {% if po_line.product == form.instance.product %}
                                                    <strong>{{ po_line.quantity_ordered }}</strong>
                                                {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% for po_line in reception.purchase_order.lines.all %}
                                                {% if po_line.product == form.instance.product %}
                                                    {{ po_line.quantity_received }}
                                                {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% for po_line in reception.purchase_order.lines.all %}
                                                {% if po_line.product == form.instance.product %}
                                                    <span class="badge bg-info">
                                                        {{ po_line.quantity_remaining }}
                                                    </span>
                                                {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {{ form.quantity_received }}
                                            {% if form.quantity_received.errors %}
                                                <div class="text-danger small mt-1">
                                                    {{ form.quantity_received.errors.0 }}
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ form.notes }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if reception.notes %}
                    <div class="mt-3">
                        <h6>Notes de la réception :</h6>
                        <p class="text-muted">{{ reception.notes|linebreaks }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'documents:reception_detail' reception.pk %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Retour
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> Valider la réception
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Alerte d'information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            <strong>Information :</strong> La validation de cette réception mettra automatiquement à jour les stocks 
            du magasin et les quantités reçues du bon de commande. Cette action ne peut pas être annulée.
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation côté client
    const form = document.querySelector('form');
    const quantityInputs = document.querySelectorAll('input[name$="-quantity_received"]');
    
    form.addEventListener('submit', function(e) {
        let hasQuantity = false;
        quantityInputs.forEach(input => {
            if (parseInt(input.value) > 0) {
                hasQuantity = true;
            }
        });
        
        if (!hasQuantity) {
            e.preventDefault();
            alert('Veuillez saisir au moins une quantité à réceptionner.');
            return false;
        }
        
        if (!confirm('Êtes-vous sûr de vouloir valider cette réception ? Cette action mettra à jour les stocks et ne pourra pas être annulée.')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-calcul des quantités restantes
    quantityInputs.forEach(input => {
        input.addEventListener('input', function() {
            const row = this.closest('tr');
            const maxQty = parseInt(row.querySelector('.badge').textContent);
            const currentQty = parseInt(this.value) || 0;
            
            if (currentQty > maxQty) {
                this.setCustomValidity(`La quantité ne peut pas dépasser ${maxQty}`);
            } else {
                this.setCustomValidity('');
            }
        });
    });
});
</script>
{% endblock %}
