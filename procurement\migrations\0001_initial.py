# Generated by Django 5.2.6 on 2025-09-13 21:29

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("inventory", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Supplier",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(max_length=200, verbose_name="Nom du fournisseur"),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Code fournisseur"
                    ),
                ),
                (
                    "contact_person",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Personne de contact"
                    ),
                ),
                (
                    "email",
                    models.EmailField(blank=True, max_length=254, verbose_name="Email"),
                ),
                (
                    "phone",
                    models.Char<PERSON>ield(
                        blank=True, max_length=20, verbose_name="Téléphone"
                    ),
                ),
                ("address", models.TextField(blank=True, verbose_name="Adresse")),
                ("is_active", models.BooleanField(default=True, verbose_name="Actif")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Fournisseur",
                "verbose_name_plural": "Fournisseurs",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Contract",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro de contrat"
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="Titre du contrat"),
                ),
                ("start_date", models.DateField(verbose_name="Date de début")),
                ("end_date", models.DateField(verbose_name="Date de fin")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Brouillon"),
                            ("active", "Actif"),
                            ("expired", "Expiré"),
                            ("terminated", "Résilié"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=12,
                        null=True,
                        verbose_name="Montant total",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Créé par",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="procurement.supplier",
                        verbose_name="Fournisseur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Contrat",
                "verbose_name_plural": "Contrats",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro de BC"
                    ),
                ),
                ("order_date", models.DateField(verbose_name="Date de commande")),
                (
                    "expected_delivery_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date de livraison prévue"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Brouillon"),
                            ("sent", "Envoyé"),
                            ("confirmed", "Confirmé"),
                            ("partially_received", "Partiellement reçu"),
                            ("fully_received", "Entièrement reçu"),
                            ("cancelled", "Annulé"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=12,
                        verbose_name="Montant total",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "contract",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="procurement.contract",
                        verbose_name="Contrat associé",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Créé par",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="procurement.supplier",
                        verbose_name="Fournisseur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bon de commande",
                "verbose_name_plural": "Bons de commande",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrderLine",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity_ordered",
                    models.PositiveIntegerField(verbose_name="Quantité commandée"),
                ),
                (
                    "quantity_received",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité reçue"
                    ),
                ),
                (
                    "unit_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="Prix unitaire",
                    ),
                ),
                (
                    "line_total",
                    models.DecimalField(
                        decimal_places=2, max_digits=12, verbose_name="Total ligne"
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lines",
                        to="procurement.purchaseorder",
                        verbose_name="Bon de commande",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ligne de commande",
                "verbose_name_plural": "Lignes de commande",
                "unique_together": {("purchase_order", "product")},
            },
        ),
    ]
