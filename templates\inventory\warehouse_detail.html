{% extends 'base.html' %}

{% block title %}{{ warehouse.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">Magasins</a></li>
                <li class="breadcrumb-item active">{{ warehouse.name }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-building"></i> 
                {{ warehouse.name }}
                {% if not warehouse.is_active %}
                    <span class="badge bg-secondary ms-2">Inactif</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                {% if user.can_manage_central_warehouse %}
                <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations du magasin -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations du magasin</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Code :</dt>
                            <dd class="col-sm-8"><code>{{ warehouse.code }}</code></dd>
                            
                            <dt class="col-sm-4">Nom :</dt>
                            <dd class="col-sm-8"><strong>{{ warehouse.name }}</strong></dd>
                            
                            <dt class="col-sm-4">Type :</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-{% if warehouse.warehouse_type == 'central' %}primary{% else %}info{% endif %} fs-6">
                                    {{ warehouse.get_warehouse_type_display }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-4">Statut :</dt>
                            <dd class="col-sm-8">
                                {% if warehouse.is_active %}
                                    <span class="badge bg-success fs-6">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary fs-6">Inactif</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Gérant :</dt>
                            <dd class="col-sm-7">
                                {% if warehouse.manager %}
                                    <div>
                                        <strong>{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</strong>
                                        <br>
                                        <small class="text-muted">{{ warehouse.manager.get_role_display }}</small>
                                        {% if warehouse.manager.email %}
                                        <br>
                                        <a href="mailto:{{ warehouse.manager.email }}" class="small">{{ warehouse.manager.email }}</a>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <span class="text-muted">Non assigné</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Magasin parent :</dt>
                            <dd class="col-sm-7">
                                {% if warehouse.parent_warehouse %}
                                    <a href="{% url 'inventory:warehouse_detail' warehouse.parent_warehouse.pk %}">
                                        {{ warehouse.parent_warehouse.name }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Créé le :</dt>
                            <dd class="col-sm-7">{{ warehouse.created_at|date:"d/m/Y à H:i" }}</dd>
                            
                            <dt class="col-sm-5">Modifié le :</dt>
                            <dd class="col-sm-7">{{ warehouse.updated_at|date:"d/m/Y à H:i" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if warehouse.address %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6><i class="bi bi-geo-alt"></i> Adresse :</h6>
                        <p class="text-muted">{{ warehouse.address|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Stocks du magasin -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-boxes"></i> Stocks du magasin</h5>
                <a href="{% url 'inventory:stock_list' %}?warehouse={{ warehouse.pk }}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-eye"></i> Voir tous les stocks
                </a>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="bi bi-boxes display-4 text-muted"></i>
                    <h5 class="mt-3">Stocks du magasin</h5>
                    <p class="text-muted mb-3">Les stocks de ce magasin s'afficheront ici.</p>
                    <a href="{% url 'inventory:stock_list' %}?warehouse={{ warehouse.pk }}" class="btn btn-primary">
                        <i class="bi bi-boxes"></i> Voir les stocks
                    </a>
                </div>
            </div>
        </div>

        <!-- Sous-magasins (si magasin central) -->
        {% if warehouse.warehouse_type == 'central' %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-diagram-3"></i> Sous-magasins</h5>
            </div>
            <div class="card-body">
                {% if warehouse.children.exists %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Nom</th>
                                <th>Gérant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for child in warehouse.children.all %}
                            <tr>
                                <td><code>{{ child.code }}</code></td>
                                <td>{{ child.name }}</td>
                                <td>
                                    {% if child.manager %}
                                        {{ child.manager.get_full_name|default:child.manager.username }}
                                    {% else %}
                                        <span class="text-muted">Non assigné</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if child.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'inventory:warehouse_detail' child.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="bi bi-diagram-3 display-4 text-muted"></i>
                    <h6 class="mt-3">Aucun sous-magasin</h6>
                    <p class="text-muted mb-0">Ce magasin central n'a pas encore de sous-magasins.</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Résumé du magasin -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clipboard-data"></i> Résumé</h6>
            </div>
            <div class="card-body text-center">
                <div class="warehouse-icon mx-auto mb-3">
                    <i class="bi bi-{% if warehouse.warehouse_type == 'central' %}house-fill{% else %}shop{% endif %}"></i>
                </div>
                <h5>{{ warehouse.name }}</h5>
                <p class="text-muted">{{ warehouse.code }}</p>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ warehouse.created_at|date:"d/m/Y" }}</h6>
                        <small class="text-muted">Créé le</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">
                            {% if warehouse.warehouse_type == 'central' %}
                                {{ warehouse.children.count }}
                            {% else %}
                                1
                            {% endif %}
                        </h6>
                        <small class="text-muted">
                            {% if warehouse.warehouse_type == 'central' %}
                                Sous-magasins
                            {% else %}
                                Sous-magasin
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> Modifier le magasin
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'inventory:stock_list' %}?warehouse={{ warehouse.pk }}" class="btn btn-outline-info">
                        <i class="bi bi-boxes"></i> Voir les stocks
                    </a>
                    
                    {% if user.can_manage_central_warehouse or warehouse.manager == user %}
                    <a href="{% url 'inventory:transfer_create' %}?from_warehouse={{ warehouse.pk }}" class="btn btn-outline-success">
                        <i class="bi bi-arrow-right"></i> Nouveau transfert
                    </a>
                    {% endif %}
                    
                    {% if warehouse.manager %}
                    <a href="mailto:{{ warehouse.manager.email }}" class="btn btn-outline-secondary">
                        <i class="bi bi-envelope"></i> Contacter le gérant
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Informations gérant -->
        {% if warehouse.manager %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person-badge"></i> Gérant</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="manager-avatar mx-auto mb-3">
                        <i class="bi bi-person-fill"></i>
                    </div>
                    <h6>{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</h6>
                    <p class="text-muted small">{{ warehouse.manager.get_role_display }}</p>
                    
                    {% if warehouse.manager.department %}
                    <p class="text-muted small">
                        <i class="bi bi-building me-1"></i>
                        {{ warehouse.manager.department }}
                    </p>
                    {% endif %}
                    
                    <div class="d-grid gap-2 mt-3">
                        {% if warehouse.manager.email %}
                        <a href="mailto:{{ warehouse.manager.email }}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-envelope"></i> Email
                        </a>
                        {% endif %}
                        
                        {% if warehouse.manager.phone %}
                        <a href="tel:{{ warehouse.manager.phone }}" class="btn btn-sm btn-outline-success">
                            <i class="bi bi-telephone"></i> Téléphone
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.warehouse-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.manager-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}
</style>
{% endblock %}
