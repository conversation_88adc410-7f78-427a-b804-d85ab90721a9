{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if order %}Modifier{% else %}Nouveau{% endif %} bon de commande - {{ block.super }}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'procurement:purchase_order_list' %}">Bons de commande</a></li>
                <li class="breadcrumb-item active">
                    {% if order %}Modifier{% else %}Nouveau{% endif %}
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-cart"></i>
            {% if order %}
                Modifier le bon de commande {{ order.number }}
            {% else %}
                Nouveau bon de commande
            {% endif %}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <form method="post" id="purchase-order-form">
            {% csrf_token %}
            
            <!-- Informations générales -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.number.id_for_label }}" class="form-label">
                                    {{ form.number.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.number }}
                                {% if form.number.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.number.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.order_date.id_for_label }}" class="form-label">
                                    {{ form.order_date.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.order_date }}
                                {% if form.order_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.order_date.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.supplier.id_for_label }}" class="form-label">
                                    {{ form.supplier.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.supplier }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.supplier.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.contract.id_for_label }}" class="form-label">
                                    {{ form.contract.label }}
                                </label>
                                {{ form.contract }}
                                <small class="form-text text-muted">
                                    Contrat associé (optionnel)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">
                                    {{ form.expected_delivery_date.label }}
                                </label>
                                {{ form.expected_delivery_date }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                    </div>
                </div>
            </div>

            <!-- Lignes de commande -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list"></i> Produits commandés</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="add-line">
                        <i class="bi bi-plus"></i> Ajouter une ligne
                    </button>
                </div>
                <div class="card-body">
                    {{ formset.management_form }}
                    
                    <div class="table-responsive">
                        <table class="table" id="formset-table">
                            <thead>
                                <tr>
                                    <th>Produit <span class="text-danger">*</span></th>
                                    <th>Quantité <span class="text-danger">*</span></th>
                                    <th>Prix unitaire <span class="text-danger">*</span></th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for form in formset %}
                                    <tr class="formset-row">
                                        {% for hidden in form.hidden_fields %}
                                            {{ hidden }}
                                        {% endfor %}
                                        <td>
                                            {{ form.product }}
                                            {% if form.product.errors %}
                                                <div class="text-danger small">{{ form.product.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ form.quantity_ordered }}
                                            {% if form.quantity_ordered.errors %}
                                                <div class="text-danger small">{{ form.quantity_ordered.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ form.unit_price }}
                                            {% if form.unit_price.errors %}
                                                <div class="text-danger small">{{ form.unit_price.errors.0 }}</div>
                                            {% endif %}
                                        </td>
                                        <td class="line-total">0.00 €</td>
                                        <td>
                                            {% if form.DELETE %}
                                                {{ form.DELETE }}
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-row">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Total général :</th>
                                    <th id="grand-total">0.00 €</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'procurement:purchase_order_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i>
                    {% if order %}Modifier{% else %}Créer{% endif %}
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <h6>Création d'un bon de commande :</h6>
                <ol class="small">
                    <li>Sélectionner le fournisseur</li>
                    <li>Ajouter les produits et quantités</li>
                    <li>Vérifier les prix unitaires</li>
                    <li>Sauvegarder le bon de commande</li>
                </ol>
                
                <hr>
                
                <h6>Informations importantes :</h6>
                <ul class="small text-muted">
                    <li>Le numéro doit être unique</li>
                    <li>Au moins un produit est requis</li>
                    <li>Les prix sont en euros TTC</li>
                    <li>Le total est calculé automatiquement</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-générer le numéro de BC si vide
    const numberField = document.getElementById('{{ form.number.id_for_label }}');
    if (numberField && !numberField.value) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
        numberField.value = `BC-${year}${month}${day}-${time}`;
    }
    
    // Définir la date du jour par défaut
    const dateField = document.getElementById('{{ form.order_date.id_for_label }}');
    if (dateField && !dateField.value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        dateField.value = `${year}-${month}-${day}`;
    }
    
    // Calcul automatique des totaux
    function updateTotals() {
        let grandTotal = 0;
        document.querySelectorAll('.formset-row').forEach(row => {
            const quantity = parseFloat(row.querySelector('input[name$="-quantity_ordered"]').value) || 0;
            const price = parseFloat(row.querySelector('input[name$="-unit_price"]').value) || 0;
            const lineTotal = quantity * price;
            
            row.querySelector('.line-total').textContent = lineTotal.toFixed(2) + ' €';
            grandTotal += lineTotal;
        });
        
        document.getElementById('grand-total').textContent = grandTotal.toFixed(2) + ' €';
    }
    
    // Événements pour le calcul des totaux
    document.addEventListener('input', function(e) {
        if (e.target.name && (e.target.name.includes('quantity_ordered') || e.target.name.includes('unit_price'))) {
            updateTotals();
        }
    });
    
    // Gestion des lignes de formset
    let formIndex = {{ formset.total_form_count }};
    
    document.getElementById('add-line').addEventListener('click', function() {
        // Logique pour ajouter une nouvelle ligne (simplifiée)
        console.log('Ajouter une ligne');
    });
    
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-row') || e.target.closest('.delete-row')) {
            const row = e.target.closest('.formset-row');
            const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
            if (deleteCheckbox) {
                deleteCheckbox.checked = true;
                row.style.display = 'none';
            }
            updateTotals();
        }
    });
    
    // Calcul initial
    updateTotals();
});
</script>
{% endblock %}
