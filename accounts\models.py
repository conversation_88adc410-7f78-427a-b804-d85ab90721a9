from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """Modèle utilisateur personnalisé avec rôles et permissions"""

    ROLE_CHOICES = [
        ('admin', 'Administrateur'),
        ('manager', 'Manager'),
        ('central_storekeeper', 'Magasinier Central'),
        ('sub_storekeeper', '<PERSON><PERSON><PERSON>'),
        ('service_requester', 'Service Demandeur'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='service_requester',
        verbose_name="Rôle"
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="Téléphone"
    )

    department = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="Service/Département"
    )

    is_active_user = models.BooleanField(
        default=True,
        verbose_name="Utilisateur actif"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Utilisateur"
        verbose_name_plural = "Utilisateurs"

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_role_display()})"

    def has_role(self, role):
        """Vérifie si l'utilisateur a un rôle spécifique"""
        return self.role == role

    def can_manage_central_warehouse(self):
        """Peut gérer le magasin central"""
        return self.role in ['admin', 'manager', 'central_storekeeper']

    def can_manage_sub_warehouse(self):
        """Peut gérer un sous-magasin"""
        return self.role in ['admin', 'manager', 'sub_storekeeper']

    def can_request_products(self):
        """Peut demander des produits"""
        return self.role in ['admin', 'manager', 'service_requester']

    def can_view_all_data(self):
        """Peut voir toutes les données"""
        return self.role in ['admin', 'manager']

    def save(self, *args, **kwargs):
        # Si c'est un superutilisateur, définir le rôle admin
        if self.is_superuser and self.role == 'service_requester':
            self.role = 'admin'
        super().save(*args, **kwargs)
