{% extends 'base.html' %}

{% block title %}Stocks - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-boxes"></i> Gestion des stocks</h1>
            <div>
                {% if user.can_manage_central_warehouse %}
                <a href="{% url 'documents:stock_adjust' %}" class="btn btn-warning">
                    <i class="bi bi-gear"></i> Ajuster stock
                </a>
                {% endif %}
                <a href="{% url 'inventory:transfer_create' %}" class="btn btn-primary">
                    <i class="bi bi-arrow-left-right"></i> Nouveau transfert
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" value="{{ search }}" 
                               class="form-control" placeholder="Rechercher un produit...">
                    </div>
                    <div class="col-md-3">
                        <select name="warehouse" class="form-control">
                            <option value="">Tous les magasins</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse.id|stringformat:"s" == warehouse_id %}selected{% endif %}>
                                {{ warehouse.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-control">
                            <option value="">Toutes les catégories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check">
                            <input type="checkbox" name="low_stock_only" value="1" 
                                   class="form-check-input" {% if low_stock_only %}checked{% endif %}>
                            <label class="form-check-label">Stock faible</label>
                        </div>
                    </div>
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-outline-secondary">
                            <i class="bi bi-search"></i> Rechercher
                        </button>
                        <a href="{% url 'inventory:stock_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Effacer
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des stocks -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Catégorie</th>
                                <th>Magasin</th>
                                <th>Stock actuel</th>
                                <th>Stock minimum</th>
                                <th>Statut</th>
                                <th>Dernière MAJ</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in page_obj %}
                            <tr {% if stock.is_low_stock %}class="table-warning"{% endif %}>
                                <td>
                                    <strong>{{ stock.product.reference }}</strong><br>
                                    <small class="text-muted">{{ stock.product.name }}</small>
                                </td>
                                <td>{{ stock.product.category.name }}</td>
                                <td>
                                    <span class="badge bg-{% if stock.warehouse.warehouse_type == 'central' %}primary{% else %}secondary{% endif %}">
                                        {{ stock.warehouse.name }}
                                    </span>
                                </td>
                                <td>
                                    <strong>{{ stock.quantity }}</strong> {{ stock.product.unit }}
                                </td>
                                <td>{{ stock.product.minimum_stock }} {{ stock.product.unit }}</td>
                                <td>
                                    {% if stock.is_low_stock %}
                                        <span class="badge bg-warning">Stock faible</span>
                                    {% elif stock.quantity == 0 %}
                                        <span class="badge bg-danger">Rupture</span>
                                    {% else %}
                                        <span class="badge bg-success">Normal</span>
                                    {% endif %}
                                </td>
                                <td>{{ stock.updated_at|date:"d/m/Y H:i" }}</td>
                                <td>
                                    <a href="{% url 'inventory:product_detail' stock.product.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    {% if user.can_manage_central_warehouse %}
                                    <a href="{% url 'documents:stock_adjust' %}?product={{ stock.product.pk }}&warehouse={{ stock.warehouse.pk }}" 
                                       class="btn btn-sm btn-outline-warning">
                                        <i class="bi bi-gear"></i> Ajuster
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if warehouse_id %}&warehouse={{ warehouse_id }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if low_stock_only %}&low_stock_only=1{% endif %}">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if warehouse_id %}&warehouse={{ warehouse_id }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if low_stock_only %}&low_stock_only=1{% endif %}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if warehouse_id %}&warehouse={{ warehouse_id }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if low_stock_only %}&low_stock_only=1{% endif %}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if warehouse_id %}&warehouse={{ warehouse_id }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if low_stock_only %}&low_stock_only=1{% endif %}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-boxes display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun stock trouvé</h3>
                    <p class="text-muted">
                        {% if search or warehouse_id or category_id or low_stock_only %}
                            Aucun stock ne correspond à vos critères de recherche.
                        {% else %}
                            Les stocks s'afficheront ici une fois que vous aurez des produits en stock.
                        {% endif %}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_products|default:0 }}</h4>
                        <p class="mb-0">Produits en stock</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box-seam display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ low_stock_count|default:0 }}</h4>
                        <p class="mb-0">Stock faible</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ out_of_stock_count|default:0 }}</h4>
                        <p class="mb-0">Rupture</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-x-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ warehouses_count|default:0 }}</h4>
                        <p class="mb-0">Magasins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
