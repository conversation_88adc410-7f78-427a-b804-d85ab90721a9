from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth.views import LoginView, LogoutView
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, DetailView
from django.core.paginator import Paginator
from django.db.models import Q
from .models import User
from .forms import CustomUserCreationForm, CustomLoginForm, UserUpdateForm
from .decorators import admin_required, manager_or_admin_required


class CustomLoginView(LoginView):
    """Vue de connexion personnalisée"""
    form_class = CustomLoginForm
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('dashboard:home')

    def form_valid(self, form):
        messages.success(self.request, f'Bienvenue {form.get_user().get_full_name()}!')
        return super().form_valid(form)


class CustomLogoutView(LogoutView):
    """Vue de déconnexion personnalisée"""
    next_page = reverse_lazy('accounts:login')

    def dispatch(self, request, *args, **kwargs):
        messages.info(request, 'Vous avez été déconnecté avec succès.')
        return super().dispatch(request, *args, **kwargs)


@login_required
def profile_view(request):
    """Vue du profil utilisateur"""
    return render(request, 'accounts/profile.html', {
        'user': request.user
    })


class UserCreateView(CreateView):
    """Vue de création d'utilisateur (pour les admins)"""
    model = User
    form_class = CustomUserCreationForm
    template_name = 'accounts/user_create.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        messages.success(self.request, 'Utilisateur créé avec succès!')
        return super().form_valid(form)


@login_required
@manager_or_admin_required
def user_list_view(request):
    """Liste des utilisateurs avec recherche et pagination"""
    search_query = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    status_filter = request.GET.get('status', '')

    users = User.objects.all()

    # Filtres de recherche
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(department__icontains=search_query)
        )

    if role_filter:
        users = users.filter(role=role_filter)

    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)

    users = users.order_by('username')

    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistiques
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    inactive_users = total_users - active_users

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'status_filter': status_filter,
        'role_choices': User.ROLE_CHOICES,
        'total_users': total_users,
        'active_users': active_users,
        'inactive_users': inactive_users,
    }

    return render(request, 'accounts/user_list.html', context)


@login_required
@manager_or_admin_required
def user_detail_view(request, pk):
    """Détail d'un utilisateur"""
    user = get_object_or_404(User, pk=pk)

    context = {
        'user_detail': user,  # Renommé pour éviter la confusion avec request.user
    }

    return render(request, 'accounts/user_detail.html', context)


class UserUpdateView(UpdateView):
    """Vue de modification d'utilisateur"""
    model = User
    form_class = UserUpdateForm
    template_name = 'accounts/user_form.html'
    success_url = reverse_lazy('accounts:user_list')

    def dispatch(self, request, *args, **kwargs):
        if not request.user.can_view_all_data():
            messages.error(request, 'Accès non autorisé.')
            return redirect('dashboard:home')
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        messages.success(self.request, f'Utilisateur {form.instance.get_full_name()} modifié avec succès!')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'Modifier {self.object.get_full_name()}'
        context['action'] = 'Modifier'
        return context


@login_required
@admin_required
def user_delete_view(request, pk):
    """Suppression d'un utilisateur (soft delete)"""
    user = get_object_or_404(User, pk=pk)

    # Empêcher la suppression de son propre compte
    if user == request.user:
        messages.error(request, 'Vous ne pouvez pas supprimer votre propre compte.')
        return redirect('accounts:user_list')

    # Empêcher la suppression du dernier admin
    if user.role == 'admin' and User.objects.filter(role='admin', is_active=True).count() <= 1:
        messages.error(request, 'Impossible de supprimer le dernier administrateur actif.')
        return redirect('accounts:user_list')

    if request.method == 'POST':
        # Soft delete - désactiver l'utilisateur au lieu de le supprimer
        user.is_active = False
        user.save()
        messages.success(request, f'Utilisateur {user.get_full_name()} désactivé avec succès.')
        return redirect('accounts:user_list')

    context = {
        'user_detail': user,
        'title': f'Désactiver {user.get_full_name()}',
    }

    return render(request, 'accounts/user_confirm_delete.html', context)


@login_required
@admin_required
def user_activate_view(request, pk):
    """Réactivation d'un utilisateur"""
    user = get_object_or_404(User, pk=pk)

    if request.method == 'POST':
        user.is_active = True
        user.save()
        messages.success(request, f'Utilisateur {user.get_full_name()} réactivé avec succès.')
        return redirect('accounts:user_list')

    context = {
        'user_detail': user,
        'title': f'Réactiver {user.get_full_name()}',
    }

    return render(request, 'accounts/user_confirm_activate.html', context)


@login_required
def user_profile_edit_view(request):
    """Modification du profil utilisateur par lui-même"""
    if request.method == 'POST':
        form = UserUpdateForm(request.POST, instance=request.user)
        # Limiter les champs que l'utilisateur peut modifier
        form.fields.pop('role', None)
        form.fields.pop('is_active', None)

        if form.is_valid():
            form.save()
            messages.success(request, 'Votre profil a été mis à jour avec succès.')
            return redirect('accounts:profile')
    else:
        form = UserUpdateForm(instance=request.user)
        # Limiter les champs que l'utilisateur peut modifier
        form.fields.pop('role', None)
        form.fields.pop('is_active', None)

    context = {
        'form': form,
        'title': 'Modifier mon profil',
        'action': 'Modifier',
    }

    return render(request, 'accounts/user_form.html', context)
