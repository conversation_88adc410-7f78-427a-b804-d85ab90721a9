{% extends 'base.html' %}

{% block title %}Mon profil - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-person-circle"></i> Mon profil</h1>
            <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Modifier mon profil
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations personnelles -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> Informations personnelles</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Nom d'utilisateur :</dt>
                            <dd class="col-sm-7"><code>{{ user.username }}</code></dd>
                            
                            <dt class="col-sm-5">Prénom :</dt>
                            <dd class="col-sm-7">{{ user.first_name|default:"-" }}</dd>
                            
                            <dt class="col-sm-5">Nom :</dt>
                            <dd class="col-sm-7">{{ user.last_name|default:"-" }}</dd>
                            
                            <dt class="col-sm-5">Email :</dt>
                            <dd class="col-sm-7">
                                {% if user.email %}
                                    <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Téléphone :</dt>
                            <dd class="col-sm-7">
                                {% if user.phone %}
                                    <a href="tel:{{ user.phone }}">{{ user.phone }}</a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Département :</dt>
                            <dd class="col-sm-7">{{ user.department|default:"-" }}</dd>
                            
                            <dt class="col-sm-5">Rôle :</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-{% if user.role == 'admin' %}danger{% elif user.role == 'manager' %}warning{% elif user.role == 'central_storekeeper' %}primary{% elif user.role == 'sub_storekeeper' %}info{% else %}secondary{% endif %} fs-6">
                                    {{ user.get_role_display }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-5">Statut :</dt>
                            <dd class="col-sm-7">
                                {% if user.is_active %}
                                    <span class="badge bg-success fs-6">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary fs-6">Inactif</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions et accès -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check"></i> Mes permissions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Permissions système :</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-{% if user.can_view_all_data %}check-circle text-success{% else %}x-circle text-muted{% endif %} me-2"></i>
                                <span class="{% if not user.can_view_all_data %}text-muted{% endif %}">
                                    Voir toutes les données
                                </span>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-{% if user.can_manage_central_warehouse %}check-circle text-success{% else %}x-circle text-muted{% endif %} me-2"></i>
                                <span class="{% if not user.can_manage_central_warehouse %}text-muted{% endif %}">
                                    Gérer le magasin central
                                </span>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-{% if user.can_manage_sub_warehouse %}check-circle text-success{% else %}x-circle text-muted{% endif %} me-2"></i>
                                <span class="{% if not user.can_manage_sub_warehouse %}text-muted{% endif %}">
                                    Gérer les sous-magasins
                                </span>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-{% if user.can_request_products %}check-circle text-success{% else %}x-circle text-muted{% endif %} me-2"></i>
                                <span class="{% if not user.can_request_products %}text-muted{% endif %}">
                                    Demander des produits
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Accès aux modules :</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <span>Tableau de bord</span>
                            </li>
                            {% if user.can_manage_central_warehouse or user.can_manage_sub_warehouse %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <span>Gestion des stocks</span>
                            </li>
                            {% endif %}
                            {% if user.can_manage_central_warehouse %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <span>Approvisionnement</span>
                            </li>
                            {% endif %}
                            {% if user.can_request_products %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <span>Demandes internes</span>
                            </li>
                            {% endif %}
                            {% if user.can_view_all_data %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <span>Gestion des utilisateurs</span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informations de compte -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations de compte</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-6">Date d'inscription :</dt>
                            <dd class="col-sm-6">{{ user.date_joined|date:"d/m/Y à H:i" }}</dd>
                            
                            <dt class="col-sm-6">Dernière connexion :</dt>
                            <dd class="col-sm-6">
                                {% if user.last_login %}
                                    {{ user.last_login|date:"d/m/Y à H:i" }}
                                {% else %}
                                    <span class="text-muted">Jamais</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-6">Dernière modification :</dt>
                            <dd class="col-sm-6">{{ user.updated_at|date:"d/m/Y à H:i" }}</dd>
                            
                            <dt class="col-sm-6">ID utilisateur :</dt>
                            <dd class="col-sm-6"><code>#{{ user.id }}</code></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Résumé du profil -->
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="avatar-circle-large mx-auto mb-3">
                    <i class="bi bi-person-fill"></i>
                </div>
                <h4>{{ user.get_full_name|default:user.username }}</h4>
                <p class="text-muted mb-3">{{ user.get_role_display }}</p>
                
                {% if user.department %}
                <p class="text-muted">
                    <i class="bi bi-building me-1"></i>
                    {{ user.department }}
                </p>
                {% endif %}
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ user.date_joined|date:"d/m/Y" }}</h6>
                        <small class="text-muted">Membre depuis</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">
                            {% if user.last_login %}
                                {{ user.last_login|timesince }}
                            {% else %}
                                Jamais
                            {% endif %}
                        </h6>
                        <small class="text-muted">Dernière visite</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> Modifier mon profil
                    </a>
                    
                    <a href="{% url 'dashboard:home' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-house"></i> Retour au tableau de bord
                    </a>
                    
                    {% if user.can_request_products %}
                    <a href="{% url 'distribution:internal_order_list' %}" class="btn btn-outline-info">
                        <i class="bi bi-cart"></i> Mes demandes
                    </a>
                    {% endif %}
                    
                    {% if user.can_view_all_data %}
                    <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-warning">
                        <i class="bi bi-people"></i> Gérer les utilisateurs
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Aide -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-question-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <h6>Votre rôle : {{ user.get_role_display }}</h6>
                <p class="small text-muted mb-3">
                    {% if user.role == 'admin' %}
                        Vous avez un accès complet au système et pouvez gérer tous les aspects de l'application.
                    {% elif user.role == 'manager' %}
                        Vous pouvez approuver les demandes et gérer les opérations générales.
                    {% elif user.role == 'central_storekeeper' %}
                        Vous gérez le stock central et les approvisionnements.
                    {% elif user.role == 'sub_storekeeper' %}
                        Vous gérez un sous-magasin spécifique.
                    {% else %}
                        Vous pouvez faire des demandes de produits pour votre service.
                    {% endif %}
                </p>
                
                <hr>
                
                <h6>Besoin d'aide ?</h6>
                <p class="small text-muted mb-0">
                    Contactez votre administrateur système ou consultez la documentation utilisateur.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}
</style>
{% endblock %}
