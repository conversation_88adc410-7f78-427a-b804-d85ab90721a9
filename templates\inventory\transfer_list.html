{% extends 'base.html' %}

{% block title %}Transferts de stock - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-arrow-left-right"></i> Transferts de stock</h1>
            {% if user.can_manage_central_warehouse or user.role == 'sub_storekeeper' %}
            <a href="{% url 'inventory:transfer_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau transfert
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        {{ form.search }}
                    </div>
                    <div class="col-md-2">
                        {{ form.from_warehouse }}
                    </div>
                    <div class="col-md-2">
                        {{ form.to_warehouse }}
                    </div>
                    <div class="col-md-2">
                        {{ form.status }}
                    </div>
                    <div class="col-md-2">
                        {{ form.date_from }}
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des transferts -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>De</th>
                                <th>Vers</th>
                                <th>Date transfert</th>
                                <th>Statut</th>
                                <th>Créé par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in page_obj %}
                            <tr>
                                <td><strong>{{ transfer.number }}</strong></td>
                                <td>
                                    <span class="badge bg-{% if transfer.from_warehouse.warehouse_type == 'central' %}primary{% else %}secondary{% endif %}">
                                        {{ transfer.from_warehouse.name }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{% if transfer.to_warehouse.warehouse_type == 'central' %}primary{% else %}secondary{% endif %}">
                                        {{ transfer.to_warehouse.name }}
                                    </span>
                                </td>
                                <td>{{ transfer.transfer_date|date:"d/m/Y" }}</td>
                                <td>
                                    {% if transfer.status == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% elif transfer.status == 'approved' %}
                                        <span class="badge bg-info">Approuvé</span>
                                    {% elif transfer.status == 'sent' %}
                                        <span class="badge bg-primary">Expédié</span>
                                    {% elif transfer.status == 'completed' %}
                                        <span class="badge bg-success">Terminé</span>
                                    {% elif transfer.status == 'cancelled' %}
                                        <span class="badge bg-danger">Annulé</span>
                                    {% endif %}
                                </td>
                                <td>{{ transfer.created_by.get_full_name }}</td>
                                <td>
                                    <a href="{% url 'inventory:transfer_detail' transfer.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    
                                    {% if transfer.status == 'pending' and user.can_manage_central_warehouse %}
                                    <a href="{% url 'inventory:transfer_approve' transfer.pk %}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-check-circle"></i> Approuver
                                    </a>
                                    {% endif %}
                                    
                                    {% if transfer.status == 'approved' and user.can_manage_central_warehouse %}
                                    <a href="{% url 'inventory:transfer_send' transfer.pk %}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-send"></i> Expédier
                                    </a>
                                    {% endif %}
                                    
                                    {% if transfer.status == 'sent' %}
                                    <a href="{% url 'inventory:transfer_receive' transfer.pk %}" 
                                       class="btn btn-sm btn-outline-warning">
                                        <i class="bi bi-inbox"></i> Réceptionner
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-arrow-left-right display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun transfert trouvé</h3>
                    <p class="text-muted">
                        {% if user.can_manage_central_warehouse or user.role == 'sub_storekeeper' %}
                            Commencez par créer votre premier transfert.
                        {% else %}
                            Les transferts s'afficheront ici.
                        {% endif %}
                    </p>
                    {% if user.can_manage_central_warehouse or user.role == 'sub_storekeeper' %}
                    <a href="{% url 'inventory:transfer_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouveau transfert
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <p class="mb-0">Total transferts</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_count|default:0 }}</h4>
                        <p class="mb-0">En attente</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ approved_count|default:0 }}</h4>
                        <p class="mb-0">Approuvés</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ completed_count|default:0 }}</h4>
                        <p class="mb-0">Terminés</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check2-all display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
