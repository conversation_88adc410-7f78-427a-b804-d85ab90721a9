{% extends 'base.html' %}

{% block title %}{{ supplier.name }} - Fournisseurs - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'procurement:supplier_list' %}">Fournisseurs</a></li>
                <li class="breadcrumb-item active">{{ supplier.name }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-building"></i> {{ supplier.name }}
                {% if supplier.is_active %}
                    <span class="badge bg-success ms-2">Actif</span>
                {% else %}
                    <span class="badge bg-secondary ms-2">Inactif</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'procurement:supplier_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                <a href="#" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations du fournisseur -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Code :</dt>
                            <dd class="col-sm-8"><strong>{{ supplier.code }}</strong></dd>
                            
                            <dt class="col-sm-4">Nom :</dt>
                            <dd class="col-sm-8">{{ supplier.name }}</dd>
                            
                            <dt class="col-sm-4">Contact :</dt>
                            <dd class="col-sm-8">{{ supplier.contact_person|default:"-" }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Email :</dt>
                            <dd class="col-sm-8">
                                {% if supplier.email %}
                                    <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                {% else %}
                                    -
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">Téléphone :</dt>
                            <dd class="col-sm-8">
                                {% if supplier.phone %}
                                    <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                {% else %}
                                    -
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">Créé le :</dt>
                            <dd class="col-sm-8">{{ supplier.created_at|date:"d/m/Y" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if supplier.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Adresse :</h6>
                        <p class="text-muted">{{ supplier.address|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Contrats récents -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-file-text"></i> Contrats récents</h5>
                <a href="{% url 'procurement:contract_create' %}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> Nouveau contrat
                </a>
            </div>
            <div class="card-body">
                {% if contracts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Titre</th>
                                <th>Période</th>
                                <th>Statut</th>
                                <th>Montant</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in contracts %}
                            <tr>
                                <td><a href="{% url 'procurement:contract_detail' contract.pk %}">{{ contract.number }}</a></td>
                                <td>{{ contract.title }}</td>
                                <td>{{ contract.start_date|date:"d/m/Y" }} - {{ contract.end_date|date:"d/m/Y" }}</td>
                                <td>
                                    <span class="badge bg-{% if contract.status == 'active' %}success{% elif contract.status == 'expired' %}danger{% else %}secondary{% endif %}">
                                        {{ contract.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if contract.total_amount %}
                                        {{ contract.total_amount|floatformat:2 }} €
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center py-3">Aucun contrat pour ce fournisseur.</p>
                {% endif %}
            </div>
        </div>

        <!-- Bons de commande récents -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-cart"></i> Bons de commande récents</h5>
                <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> Nouveau BC
                </a>
            </div>
            <div class="card-body">
                {% if purchase_orders %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Montant</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in purchase_orders %}
                            <tr>
                                <td><a href="{% url 'procurement:purchase_order_detail' order.pk %}">{{ order.number }}</a></td>
                                <td>{{ order.order_date|date:"d/m/Y" }}</td>
                                <td>
                                    <span class="badge bg-{% if order.status == 'fully_received' %}success{% elif order.status == 'cancelled' %}danger{% else %}primary{% endif %}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ order.total_amount|floatformat:2 }} €</td>
                                <td>
                                    <a href="{% url 'procurement:purchase_order_detail' order.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center py-3">Aucun bon de commande pour ce fournisseur.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Actions rapides -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'procurement:contract_create' %}" class="btn btn-outline-primary">
                        <i class="bi bi-file-text"></i> Nouveau contrat
                    </a>
                    <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-outline-success">
                        <i class="bi bi-cart-plus"></i> Nouveau BC
                    </a>
                    {% if supplier.email %}
                    <a href="mailto:{{ supplier.email }}" class="btn btn-outline-info">
                        <i class="bi bi-envelope"></i> Envoyer un email
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-graph-up"></i> Statistiques</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ contracts.count }}</h4>
                        <small class="text-muted">Contrats</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ purchase_orders.count }}</h4>
                        <small class="text-muted">Bons de commande</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
