from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, F, Sum
from django.core.paginator import Paginator
from django.db import transaction
from django.http import JsonResponse
from accounts.decorators import storekeeper_required, central_storekeeper_required
from .models import Product, ProductCategory, Warehouse, Stock, StockTransfer, StockTransferLine
from .forms import (ProductForm, ProductCategoryForm, WarehouseForm, StockTransferForm,
                   StockTransferLineFormSet, StockSearchForm, StockTransferSearchForm)
from documents.models import StockMovement


@login_required
@storekeeper_required
def product_list(request):
    """Liste des produits"""
    search = request.GET.get('search', '')
    category_id = request.GET.get('category', '')

    products = Product.objects.select_related('category')

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(reference__icontains=search)
        )

    if category_id:
        products = products.filter(category_id=category_id)

    products = products.order_by('name')
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'inventory/product_list.html', {
        'page_obj': page_obj,
        'search': search,
        'category_id': category_id,
        'categories': ProductCategory.objects.all()
    })


@login_required
@central_storekeeper_required
def product_create(request):
    """Création d'un produit"""
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            product = form.save()
            messages.success(request, f'Produit "{product.name}" créé avec succès.')
            return redirect('inventory:product_detail', pk=product.pk)
    else:
        form = ProductForm()

    return render(request, 'inventory/product_form.html', {'form': form})


@login_required
@storekeeper_required
def product_detail(request, pk):
    """Détail d'un produit"""
    product = get_object_or_404(Product, pk=pk)
    stocks = Stock.objects.filter(product=product).select_related('warehouse')
    total_stock = stocks.aggregate(total=Sum('quantity'))['total'] or 0

    return render(request, 'inventory/product_detail.html', {
        'product': product,
        'stocks': stocks,
        'total_stock': total_stock
    })


@login_required
@central_storekeeper_required
def product_edit(request, pk):
    """Modification d'un produit"""
    product = get_object_or_404(Product, pk=pk)

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, f'Produit "{product.name}" modifié avec succès.')
            return redirect('inventory:product_detail', pk=product.pk)
    else:
        form = ProductForm(instance=product)

    return render(request, 'inventory/product_form.html', {
        'form': form,
        'product': product
    })


@login_required
@storekeeper_required
def warehouse_list(request):
    """Liste des magasins"""
    warehouses = Warehouse.objects.filter(is_active=True).order_by('name')
    return render(request, 'inventory/warehouse_list.html', {'warehouses': warehouses})


@login_required
@central_storekeeper_required
def warehouse_create(request):
    """Création d'un magasin"""
    if request.method == 'POST':
        form = WarehouseForm(request.POST)
        if form.is_valid():
            warehouse = form.save()
            messages.success(request, f'Magasin "{warehouse.name}" créé avec succès.')
            return redirect('inventory:warehouse_detail', pk=warehouse.pk)
    else:
        form = WarehouseForm()

    return render(request, 'inventory/warehouse_form.html', {'form': form})


@login_required
@storekeeper_required
def warehouse_detail(request, pk):
    """Détail d'un magasin"""
    warehouse = get_object_or_404(Warehouse, pk=pk)
    return render(request, 'inventory/warehouse_detail.html', {'warehouse': warehouse})


@login_required
@central_storekeeper_required
def warehouse_edit(request, pk):
    """Modification d'un magasin"""
    warehouse = get_object_or_404(Warehouse, pk=pk)

    if request.method == 'POST':
        form = WarehouseForm(request.POST, instance=warehouse)
        if form.is_valid():
            form.save()
            messages.success(request, f'Magasin "{warehouse.name}" modifié avec succès.')
            return redirect('inventory:warehouse_detail', pk=warehouse.pk)
    else:
        form = WarehouseForm(instance=warehouse)

    return render(request, 'inventory/warehouse_form.html', {
        'form': form,
        'warehouse': warehouse
    })


@login_required
@storekeeper_required
def stock_list(request):
    """Liste des stocks"""
    form = StockSearchForm(request.GET)
    stocks = Stock.objects.select_related('product__category', 'warehouse')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        warehouse = form.cleaned_data.get('warehouse')
        category = form.cleaned_data.get('category')
        low_stock_only = form.cleaned_data.get('low_stock_only')

        if search:
            stocks = stocks.filter(
                Q(product__name__icontains=search) |
                Q(product__reference__icontains=search)
            )
        if warehouse:
            stocks = stocks.filter(warehouse=warehouse)
        if category:
            stocks = stocks.filter(product__category=category)
        if low_stock_only:
            stocks = stocks.filter(quantity__lt=F('product__minimum_stock'))

    # Filtrer selon les permissions de l'utilisateur
    if request.user.role == 'sub_storekeeper':
        stocks = stocks.filter(warehouse__manager=request.user)

    stocks = stocks.order_by('warehouse__name', 'product__name')
    paginator = Paginator(stocks, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistiques
    all_stocks = Stock.objects.select_related('product')
    if request.user.role == 'sub_storekeeper':
        all_stocks = all_stocks.filter(warehouse__manager=request.user)

    total_products = all_stocks.count()
    low_stock_count = all_stocks.filter(quantity__lt=F('product__minimum_stock')).count()
    out_of_stock_count = all_stocks.filter(quantity=0).count()
    warehouses_count = Warehouse.objects.filter(is_active=True).count()

    return render(request, 'inventory/stock_list.html', {
        'page_obj': page_obj,
        'form': form,
        'warehouses': Warehouse.objects.filter(is_active=True),
        'categories': ProductCategory.objects.all(),
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'warehouses_count': warehouses_count,
        'search': form.cleaned_data.get('search', '') if form.is_valid() else '',
        'warehouse_id': form.cleaned_data.get('warehouse').id if form.is_valid() and form.cleaned_data.get('warehouse') else '',
        'category_id': form.cleaned_data.get('category').id if form.is_valid() and form.cleaned_data.get('category') else '',
        'low_stock_only': form.cleaned_data.get('low_stock_only', False) if form.is_valid() else False,
    })


@login_required
@central_storekeeper_required
def stock_adjust(request):
    """Ajustement de stock"""
    return render(request, 'inventory/stock_adjust.html')


@login_required
@storekeeper_required
def transfer_list(request):
    """Liste des transferts"""
    form = StockTransferSearchForm(request.GET)
    transfers = StockTransfer.objects.select_related(
        'from_warehouse', 'to_warehouse', 'created_by'
    )

    if form.is_valid():
        search = form.cleaned_data.get('search')
        from_warehouse = form.cleaned_data.get('from_warehouse')
        to_warehouse = form.cleaned_data.get('to_warehouse')
        status = form.cleaned_data.get('status')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            transfers = transfers.filter(number__icontains=search)
        if from_warehouse:
            transfers = transfers.filter(from_warehouse=from_warehouse)
        if to_warehouse:
            transfers = transfers.filter(to_warehouse=to_warehouse)
        if status:
            transfers = transfers.filter(status=status)
        if date_from:
            transfers = transfers.filter(transfer_date__gte=date_from)
        if date_to:
            transfers = transfers.filter(transfer_date__lte=date_to)

    transfers = transfers.order_by('-created_at')
    paginator = Paginator(transfers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'inventory/transfer_list.html', {
        'page_obj': page_obj,
        'form': form
    })


@login_required
@storekeeper_required
def transfer_create(request):
    """Création d'un transfert"""
    if request.method == 'POST':
        form = StockTransferForm(request.POST, user=request.user)
        formset = StockTransferLineFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            transfer = form.save(commit=False)
            transfer.created_by = request.user
            transfer.save()

            formset.instance = transfer
            formset.save()

            messages.success(request, f'Transfert "{transfer.number}" créé avec succès.')
            return redirect('inventory:transfer_detail', pk=transfer.pk)
    else:
        form = StockTransferForm(user=request.user)
        formset = StockTransferLineFormSet()

    return render(request, 'inventory/transfer_form.html', {
        'form': form,
        'formset': formset
    })


@login_required
@storekeeper_required
def transfer_edit(request, pk):
    """Modification d'un transfert"""
    transfer = get_object_or_404(StockTransfer, pk=pk)

    if transfer.status != 'pending':
        messages.error(request, 'Seuls les transferts en attente peuvent être modifiés.')
        return redirect('inventory:transfer_detail', pk=pk)

    if request.method == 'POST':
        form = StockTransferForm(request.POST, instance=transfer, user=request.user)
        formset = StockTransferLineFormSet(request.POST, instance=transfer)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            messages.success(request, f'Transfert "{transfer.number}" modifié avec succès.')
            return redirect('inventory:transfer_detail', pk=transfer.pk)
    else:
        form = StockTransferForm(instance=transfer, user=request.user)
        formset = StockTransferLineFormSet(instance=transfer)

    return render(request, 'inventory/transfer_form.html', {
        'form': form,
        'formset': formset,
        'transfer': transfer
    })


@login_required
@storekeeper_required
def transfer_detail(request, pk):
    """Détail d'un transfert"""
    transfer = get_object_or_404(StockTransfer, pk=pk)
    lines = transfer.lines.select_related('product').all()

    return render(request, 'inventory/transfer_detail.html', {
        'transfer': transfer,
        'lines': lines
    })


@login_required
@central_storekeeper_required
def transfer_approve(request, pk):
    """Approbation d'un transfert"""
    transfer = get_object_or_404(StockTransfer, pk=pk)

    if transfer.status != 'pending':
        messages.error(request, 'Ce transfert ne peut plus être approuvé.')
        return redirect('inventory:transfer_detail', pk=pk)

    if request.method == 'POST':
        with transaction.atomic():
            # Vérifier la disponibilité des stocks
            for line in transfer.lines.all():
                try:
                    stock = Stock.objects.get(
                        warehouse=transfer.from_warehouse,
                        product=line.product
                    )
                    if stock.quantity < line.quantity_requested:
                        messages.error(
                            request,
                            f'Stock insuffisant pour {line.product.name}: '
                            f'{stock.quantity} disponible, {line.quantity_requested} demandé'
                        )
                        return redirect('inventory:transfer_detail', pk=pk)
                except Stock.DoesNotExist:
                    messages.error(
                        request,
                        f'Aucun stock disponible pour {line.product.name} dans {transfer.from_warehouse.name}'
                    )
                    return redirect('inventory:transfer_detail', pk=pk)

            # Approuver le transfert
            transfer.status = 'approved'
            transfer.approved_by = request.user
            transfer.save()

            messages.success(request, f'Transfert "{transfer.number}" approuvé avec succès.')

    return redirect('inventory:transfer_detail', pk=pk)


@login_required
@storekeeper_required
def transfer_send(request, pk):
    """Expédition d'un transfert"""
    transfer = get_object_or_404(StockTransfer, pk=pk)

    if transfer.status != 'approved':
        messages.error(request, 'Ce transfert doit être approuvé avant expédition.')
        return redirect('inventory:transfer_detail', pk=pk)

    if request.method == 'POST':
        with transaction.atomic():
            # Déduire les quantités du stock source
            for line in transfer.lines.all():
                stock = Stock.objects.get(
                    warehouse=transfer.from_warehouse,
                    product=line.product
                )
                stock.quantity -= line.quantity_requested
                stock.save()

                # Créer un mouvement de stock sortant
                StockMovement.objects.create(
                    movement_type='transfer_out',
                    product=line.product,
                    warehouse=transfer.from_warehouse,
                    quantity=-line.quantity_requested,
                    reference_number=transfer.number,
                    content_object=transfer,
                    created_by=request.user,
                    notes=f'Transfert vers {transfer.to_warehouse.name}'
                )

            # Marquer comme expédié
            transfer.status = 'sent'
            transfer.sent_by = request.user
            transfer.save()

            messages.success(request, f'Transfert "{transfer.number}" expédié avec succès.')

    return redirect('inventory:transfer_detail', pk=pk)


@login_required
@storekeeper_required
def transfer_receive(request, pk):
    """Réception d'un transfert"""
    transfer = get_object_or_404(StockTransfer, pk=pk)

    if transfer.status != 'sent':
        messages.error(request, 'Ce transfert doit être expédié avant réception.')
        return redirect('inventory:transfer_detail', pk=pk)

    if request.method == 'POST':
        with transaction.atomic():
            # Ajouter les quantités au stock destination
            for line in transfer.lines.all():
                stock, created = Stock.objects.get_or_create(
                    warehouse=transfer.to_warehouse,
                    product=line.product,
                    defaults={'quantity': 0}
                )
                stock.quantity += line.quantity_requested
                stock.save()

                # Créer un mouvement de stock entrant
                StockMovement.objects.create(
                    movement_type='transfer_in',
                    product=line.product,
                    warehouse=transfer.to_warehouse,
                    quantity=line.quantity_requested,
                    reference_number=transfer.number,
                    content_object=transfer,
                    created_by=request.user,
                    notes=f'Transfert depuis {transfer.from_warehouse.name}'
                )

            # Marquer comme reçu
            transfer.status = 'completed'
            transfer.received_by = request.user
            transfer.save()

            messages.success(request, f'Transfert "{transfer.number}" reçu avec succès.')

    return redirect('inventory:transfer_detail', pk=pk)
