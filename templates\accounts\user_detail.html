{% extends 'base.html' %}

{% block title %}{{ user_detail.get_full_name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'accounts:user_list' %}">Utilisateurs</a></li>
                <li class="breadcrumb-item active">{{ user_detail.get_full_name|default:user_detail.username }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-person-circle"></i> 
                {{ user_detail.get_full_name|default:user_detail.username }}
                {% if not user_detail.is_active %}
                    <span class="badge bg-secondary ms-2">Inactif</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                {% if user.role == 'admin' or user.role == 'manager' %}
                <a href="{% url 'accounts:user_edit' user_detail.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                {% endif %}
                {% if user.role == 'admin' and user_detail != user %}
                    {% if user_detail.is_active %}
                    <a href="{% url 'accounts:user_delete' user_detail.pk %}" class="btn btn-danger">
                        <i class="bi bi-person-x"></i> Désactiver
                    </a>
                    {% else %}
                    <a href="{% url 'accounts:user_activate' user_detail.pk %}" class="btn btn-success">
                        <i class="bi bi-person-check"></i> Réactiver
                    </a>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations personnelles -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> Informations personnelles</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Nom d'utilisateur :</dt>
                            <dd class="col-sm-8"><code>{{ user_detail.username }}</code></dd>
                            
                            <dt class="col-sm-4">Prénom :</dt>
                            <dd class="col-sm-8">{{ user_detail.first_name|default:"-" }}</dd>
                            
                            <dt class="col-sm-4">Nom :</dt>
                            <dd class="col-sm-8">{{ user_detail.last_name|default:"-" }}</dd>
                            
                            <dt class="col-sm-4">Email :</dt>
                            <dd class="col-sm-8">
                                {% if user_detail.email %}
                                    <a href="mailto:{{ user_detail.email }}">{{ user_detail.email }}</a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Téléphone :</dt>
                            <dd class="col-sm-8">
                                {% if user_detail.phone %}
                                    <a href="tel:{{ user_detail.phone }}">{{ user_detail.phone }}</a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">Département :</dt>
                            <dd class="col-sm-8">{{ user_detail.department|default:"-" }}</dd>
                            
                            <dt class="col-sm-4">Rôle :</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-{% if user_detail.role == 'admin' %}danger{% elif user_detail.role == 'manager' %}warning{% elif user_detail.role == 'central_storekeeper' %}primary{% elif user_detail.role == 'sub_storekeeper' %}info{% else %}secondary{% endif %} fs-6">
                                    {{ user_detail.get_role_display }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-4">Statut :</dt>
                            <dd class="col-sm-8">
                                {% if user_detail.is_active %}
                                    <span class="badge bg-success fs-6">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary fs-6">Inactif</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions et accès -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check"></i> Permissions et accès</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Permissions système :</h6>
                        <ul class="list-unstyled">
                            <li>
                                <i class="bi bi-{% if user_detail.can_view_all_data %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Voir toutes les données
                            </li>
                            <li>
                                <i class="bi bi-{% if user_detail.can_manage_central_warehouse %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Gérer le magasin central
                            </li>
                            <li>
                                <i class="bi bi-{% if user_detail.can_manage_sub_warehouse %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Gérer les sous-magasins
                            </li>
                            <li>
                                <i class="bi bi-{% if user_detail.can_request_products %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Demander des produits
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Statuts Django :</h6>
                        <ul class="list-unstyled">
                            <li>
                                <i class="bi bi-{% if user_detail.is_superuser %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Superutilisateur
                            </li>
                            <li>
                                <i class="bi bi-{% if user_detail.is_staff %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Accès administration
                            </li>
                            <li>
                                <i class="bi bi-{% if user_detail.is_active %}check-circle text-success{% else %}x-circle text-muted{% endif %}"></i>
                                Compte actif
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activité récente -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Activité récente</h5>
            </div>
            <div class="card-body">
                {% comment %}
                <!-- Ici on pourrait ajouter l'historique des actions de l'utilisateur -->
                {% endcomment %}
                <div class="text-center py-3">
                    <i class="bi bi-clock-history display-4 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">Historique d'activité non disponible</p>
                    <small class="text-muted">Cette fonctionnalité sera ajoutée dans une future version</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Résumé -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Résumé</h6>
            </div>
            <div class="card-body text-center">
                <div class="avatar-circle-large mx-auto mb-3">
                    <i class="bi bi-person-fill"></i>
                </div>
                <h5>{{ user_detail.get_full_name|default:user_detail.username }}</h5>
                <p class="text-muted">{{ user_detail.get_role_display }}</p>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ user_detail.date_joined|date:"d/m/Y" }}</h6>
                        <small class="text-muted">Inscription</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">
                            {% if user_detail.last_login %}
                                {{ user_detail.last_login|date:"d/m/Y" }}
                            {% else %}
                                Jamais
                            {% endif %}
                        </h6>
                        <small class="text-muted">Dernière connexion</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user_detail.email %}
                    <a href="mailto:{{ user_detail.email }}" class="btn btn-outline-primary">
                        <i class="bi bi-envelope"></i> Envoyer un email
                    </a>
                    {% endif %}
                    
                    {% if user_detail.phone %}
                    <a href="tel:{{ user_detail.phone }}" class="btn btn-outline-success">
                        <i class="bi bi-telephone"></i> Appeler
                    </a>
                    {% endif %}
                    
                    {% if user.role == 'admin' or user.role == 'manager' %}
                    <a href="{% url 'accounts:user_edit' user_detail.pk %}" class="btn btn-outline-secondary">
                        <i class="bi bi-pencil"></i> Modifier les informations
                    </a>
                    {% endif %}
                    
                    {% if user.role == 'admin' and user_detail != user %}
                        {% if user_detail.is_active %}
                        <a href="{% url 'accounts:user_delete' user_detail.pk %}" class="btn btn-outline-danger">
                            <i class="bi bi-person-x"></i> Désactiver le compte
                        </a>
                        {% else %}
                        <a href="{% url 'accounts:user_activate' user_detail.pk %}" class="btn btn-outline-success">
                            <i class="bi bi-person-check"></i> Réactiver le compte
                        </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}
</style>
{% endblock %}
