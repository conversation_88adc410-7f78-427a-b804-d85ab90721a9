{% extends 'base.html' %}

{% block title %}Fournisseurs - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-building"></i> Fournisseurs</h1>
            <a href="{% url 'procurement:supplier_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau fournisseur
            </a>
        </div>
    </div>
</div>

<!-- Barre de recherche -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" name="search" value="{{ search }}" 
                   class="form-control me-2" placeholder="Rechercher un fournisseur...">
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
            </button>
        </form>
    </div>
</div>

<!-- Liste des fournisseurs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Nom</th>
                                <th>Contact</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in page_obj %}
                            <tr>
                                <td><strong>{{ supplier.code }}</strong></td>
                                <td>{{ supplier.name }}</td>
                                <td>{{ supplier.contact_person|default:"-" }}</td>
                                <td>
                                    {% if supplier.email %}
                                        <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ supplier.phone|default:"-" }}</td>
                                <td>
                                    {% if supplier.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'procurement:supplier_detail' supplier.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-building display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun fournisseur trouvé</h3>
                    <p class="text-muted">
                        {% if search %}
                            Aucun fournisseur ne correspond à votre recherche.
                        {% else %}
                            Commencez par ajouter votre premier fournisseur.
                        {% endif %}
                    </p>
                    <a href="{% url 'procurement:supplier_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouveau fournisseur
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
