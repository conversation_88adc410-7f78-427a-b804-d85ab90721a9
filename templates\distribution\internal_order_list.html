{% extends 'base.html' %}

{% block title %}Commandes internes - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-clipboard-check"></i> Commandes internes (BCN)</h1>
            {% if user.can_request_products %}
            <a href="{% url 'distribution:internal_order_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouvelle commande
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        {{ form.search }}
                    </div>
                    <div class="col-md-2">
                        {{ form.requesting_service }}
                    </div>
                    <div class="col-md-2">
                        {{ form.status }}
                    </div>
                    <div class="col-md-2">
                        {{ form.priority }}
                    </div>
                    <div class="col-md-2">
                        {{ form.date_from }}
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des commandes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Service demandeur</th>
                                <th>Date création</th>
                                <th>Livraison souhaitée</th>
                                <th>Priorité</th>
                                <th>Statut</th>
                                <th>Créé par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in page_obj %}
                            <tr>
                                <td><strong>{{ order.number }}</strong></td>
                                <td>{{ order.requesting_service.name }}</td>
                                <td>{{ order.created_at|date:"d/m/Y" }}</td>
                                <td>{{ order.requested_delivery_date|date:"d/m/Y"|default:"-" }}</td>
                                <td>
                                    {% if order.priority == 'urgent' %}
                                        <span class="badge bg-danger">Urgent</span>
                                    {% elif order.priority == 'high' %}
                                        <span class="badge bg-warning">Élevée</span>
                                    {% elif order.priority == 'normal' %}
                                        <span class="badge bg-info">Normale</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Faible</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if order.status == 'draft' %}
                                        <span class="badge bg-secondary">Brouillon</span>
                                    {% elif order.status == 'pending_approval' %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% elif order.status == 'approved' %}
                                        <span class="badge bg-success">Approuvée</span>
                                    {% elif order.status == 'rejected' %}
                                        <span class="badge bg-danger">Rejetée</span>
                                    {% elif order.status == 'partially_delivered' %}
                                        <span class="badge bg-info">Partiellement livrée</span>
                                    {% elif order.status == 'fully_delivered' %}
                                        <span class="badge bg-success">Livrée</span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-dark">Annulée</span>
                                    {% endif %}
                                </td>
                                <td>{{ order.created_by.get_full_name }}</td>
                                <td>
                                    <a href="{% url 'distribution:internal_order_detail' order.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    {% if order.status == 'draft' and order.created_by == user %}
                                    <a href="{% url 'distribution:internal_order_edit' order.pk %}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </a>
                                    {% endif %}
                                    {% if order.status == 'pending_approval' and user.role == 'manager' %}
                                    <a href="{% url 'distribution:internal_order_approve' order.pk %}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-check-circle"></i> Approuver
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-clipboard-check display-1 text-muted"></i>
                    <h3 class="mt-3">Aucune commande trouvée</h3>
                    <p class="text-muted">
                        {% if user.can_request_products %}
                            Commencez par créer votre première commande interne.
                        {% else %}
                            Les commandes internes s'afficheront ici.
                        {% endif %}
                    </p>
                    {% if user.can_request_products %}
                    <a href="{% url 'distribution:internal_order_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouvelle commande
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <p class="mb-0">Total commandes</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard-check display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_count|default:0 }}</h4>
                        <p class="mb-0">En attente</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ approved_count|default:0 }}</h4>
                        <p class="mb-0">Approuvées</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ delivered_count|default:0 }}</h4>
                        <p class="mb-0">Livrées</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-truck display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
