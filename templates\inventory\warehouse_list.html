{% extends 'base.html' %}

{% block title %}Magasins - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-building"></i> Magasins</h1>
            {% if user.can_manage_central_warehouse %}
            <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau magasin
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ warehouses|length }}</h4>
                        <p class="mb-0">Total magasins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{% for w in warehouses %}{% if w.warehouse_type == 'central' %}{{ forloop.counter }}{% endif %}{% endfor %}</h4>
                        <p class="mb-0">Magasins centraux</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-house-fill display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{% for w in warehouses %}{% if w.warehouse_type == 'sub' %}{{ forloop.counter }}{% endif %}{% endfor %}</h4>
                        <p class="mb-0">Sous-magasins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-shop display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{% for w in warehouses %}{% if w.manager %}{{ forloop.counter }}{% endif %}{% endfor %}</h4>
                        <p class="mb-0">Avec gérant</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-badge display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des magasins -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if warehouses %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Nom</th>
                                <th>Type</th>
                                <th>Gérant</th>
                                <th>Magasin parent</th>
                                <th>Adresse</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for warehouse in warehouses %}
                            <tr>
                                <td><strong>{{ warehouse.code }}</strong></td>
                                <td>{{ warehouse.name }}</td>
                                <td>
                                    <span class="badge bg-{% if warehouse.warehouse_type == 'central' %}primary{% else %}info{% endif %}">
                                        {{ warehouse.get_warehouse_type_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if warehouse.manager %}
                                        <div>
                                            <strong>{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</strong>
                                            <br>
                                            <small class="text-muted">{{ warehouse.manager.get_role_display }}</small>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Non assigné</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if warehouse.parent_warehouse %}
                                        <span class="badge bg-secondary">{{ warehouse.parent_warehouse.name }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if warehouse.address %}
                                        <small class="text-muted">{{ warehouse.address|truncatechars:50 }}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if warehouse.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'inventory:warehouse_detail' warehouse.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="Voir">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        {% if user.can_manage_central_warehouse %}
                                        <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}" 
                                           class="btn btn-sm btn-outline-secondary" title="Modifier">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'inventory:stock_list' %}?warehouse={{ warehouse.pk }}" 
                                           class="btn btn-sm btn-outline-info" title="Voir les stocks">
                                            <i class="bi bi-boxes"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-building display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun magasin trouvé</h3>
                    <p class="text-muted">Commencez par créer votre premier magasin.</p>
                    {% if user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouveau magasin
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Magasins par type -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-house-fill"></i> Magasins centraux</h5>
            </div>
            <div class="card-body">
                {% for warehouse in warehouses %}
                    {% if warehouse.warehouse_type == 'central' %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ warehouse.name }}</strong>
                            <br>
                            <small class="text-muted">{{ warehouse.code }}</small>
                        </div>
                        <div>
                            {% if warehouse.manager %}
                                <span class="badge bg-success">{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</span>
                            {% else %}
                                <span class="badge bg-warning">Sans gérant</span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted mb-0">Aucun magasin central</p>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shop"></i> Sous-magasins</h5>
            </div>
            <div class="card-body">
                {% for warehouse in warehouses %}
                    {% if warehouse.warehouse_type == 'sub' %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ warehouse.name }}</strong>
                            <br>
                            <small class="text-muted">{{ warehouse.code }} - Parent: {{ warehouse.parent_warehouse.name|default:"N/A" }}</small>
                        </div>
                        <div>
                            {% if warehouse.manager %}
                                <span class="badge bg-success">{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</span>
                            {% else %}
                                <span class="badge bg-warning">Sans gérant</span>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted mb-0">Aucun sous-magasin</p>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
