<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion Produits de Nettoyage{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard:home' %}">
                <i class="bi bi-droplet-fill"></i> GPN
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:home' %}">
                            <i class="bi bi-house"></i> Tableau de bord
                        </a>
                    </li>
                    
                    {% if user.can_view_all_data or user.can_manage_central_warehouse %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-box"></i> Inventaire
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'inventory:product_list' %}">Produits</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:warehouse_list' %}">Magasins</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_list' %}">Stocks</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:transfer_list' %}">Transferts</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if user.can_manage_central_warehouse %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-cart"></i> Approvisionnement
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'procurement:supplier_list' %}">Fournisseurs</a></li>
                            <li><a class="dropdown-item" href="{% url 'procurement:contract_list' %}">Contrats</a></li>
                            <li><a class="dropdown-item" href="{% url 'procurement:purchase_order_list' %}">Bons de commande</a></li>
                            <li><a class="dropdown-item" href="{% url 'documents:reception_list' %}">Réceptions</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-truck"></i> Distribution
                        </a>
                        <ul class="dropdown-menu">
                            {% if user.can_request_products %}
                            <li><a class="dropdown-item" href="{% url 'distribution:internal_order_list' %}">Mes demandes</a></li>
                            {% endif %}
                            {% if user.can_manage_central_warehouse or user.can_manage_sub_warehouse %}
                            <li><a class="dropdown-item" href="{% url 'distribution:internal_order_manage' %}">Gérer les demandes</a></li>
                            <li><a class="dropdown-item" href="{% url 'distribution:delivery_note_list' %}">Bons de livraison</a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'distribution:service_list' %}">Services internes</a></li>
                        </ul>
                    </li>
                    
                    {% if user.can_view_all_data %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-bar-chart"></i> Rapports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'dashboard:statistics' %}">Statistiques</a></li>
                            <li><a class="dropdown-item" href="{% url 'dashboard:reports' %}">Rapports</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if user.role == 'admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i> Administration
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:user_list' %}">Utilisateurs</a></li>
                            <li><a class="dropdown-item" href="/admin/">Admin Django</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">{{ user.get_role_display }}</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">Mon profil</a></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Messages -->
    {% if messages %}
    <div class="container-fluid mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 Système de Gestion des Produits de Nettoyage. Tous droits réservés.</p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
