# Generated by Django 5.2.6 on 2025-09-13 21:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("inventory", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="InternalOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro BCN"
                    ),
                ),
                ("request_date", models.DateField(verbose_name="Date de demande")),
                (
                    "needed_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date de besoin"
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Basse"),
                            ("normal", "Normale"),
                            ("high", "Haute"),
                            ("urgent", "Urgente"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="Priorité",
                    ),
                ),
                (
                    "status",
                    models.<PERSON>r<PERSON><PERSON>(
                        choices=[
                            ("draft", "Brouillon"),
                            ("pending_approval", "En attente d'approbation"),
                            ("approved", "Approuvé"),
                            ("partially_delivered", "Partiellement livré"),
                            ("fully_delivered", "Entièrement livré"),
                            ("cancelled", "Annulé"),
                            ("rejected", "Rejeté"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                (
                    "justification",
                    models.TextField(verbose_name="Justification de la demande"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "approval_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Date d'approbation"
                    ),
                ),
                (
                    "rejection_reason",
                    models.TextField(blank=True, verbose_name="Motif de rejet"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_orders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approuvé par",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="requested_orders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Demandé par",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.warehouse",
                        verbose_name="Magasin de livraison",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bon de commande interne",
                "verbose_name_plural": "Bons de commande internes",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DeliveryNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro BDL"
                    ),
                ),
                ("delivery_date", models.DateField(verbose_name="Date de livraison")),
                (
                    "received_by_name",
                    models.CharField(max_length=100, verbose_name="Reçu par (nom)"),
                ),
                (
                    "received_by_signature",
                    models.TextField(blank=True, verbose_name="Signature (base64)"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Brouillon"),
                            ("delivered", "Livré"),
                            ("partially_delivered", "Partiellement livré"),
                            ("cancelled", "Annulé"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "delivered_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="delivered_orders",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Livré par",
                    ),
                ),
                (
                    "internal_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="distribution.internalorder",
                        verbose_name="BCN associé",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bon de livraison",
                "verbose_name_plural": "Bons de livraison",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="InternalService",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Nom du service"),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Code service"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Actif")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "manager",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Responsable",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service interne",
                "verbose_name_plural": "Services internes",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="internalorder",
            name="requesting_service",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="distribution.internalservice",
                verbose_name="Service demandeur",
            ),
        ),
        migrations.CreateModel(
            name="InternalOrderLine",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity_requested",
                    models.PositiveIntegerField(verbose_name="Quantité demandée"),
                ),
                (
                    "quantity_approved",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité approuvée"
                    ),
                ),
                (
                    "quantity_delivered",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité livrée"
                    ),
                ),
                (
                    "justification",
                    models.TextField(blank=True, verbose_name="Justification"),
                ),
                (
                    "internal_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lines",
                        to="distribution.internalorder",
                        verbose_name="BCN",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ligne BCN",
                "verbose_name_plural": "Lignes BCN",
                "unique_together": {("internal_order", "product")},
            },
        ),
    ]
