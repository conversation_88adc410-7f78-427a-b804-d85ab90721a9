{% extends 'base.html' %}

{% block title %}Transfert {{ transfer.number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:transfer_list' %}">Transferts</a></li>
                <li class="breadcrumb-item active">{{ transfer.number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-arrow-left-right"></i> 
                Transfert {{ transfer.number }}
                <span class="badge bg-{% if transfer.status == 'pending' %}warning{% elif transfer.status == 'approved' %}info{% elif transfer.status == 'sent' %}primary{% elif transfer.status == 'received' %}success{% else %}secondary{% endif %} ms-2">
                    {{ transfer.get_status_display }}
                </span>
            </h1>
            <div>
                <a href="{% url 'inventory:transfer_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                
                {% if transfer.status == 'pending' and user.can_manage_central_warehouse %}
                <a href="{% url 'inventory:transfer_edit' transfer.pk %}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                <a href="{% url 'inventory:transfer_approve' transfer.pk %}" class="btn btn-success">
                    <i class="bi bi-check-circle"></i> Approuver
                </a>
                {% endif %}
                
                {% if transfer.status == 'approved' and user.can_manage_central_warehouse %}
                <a href="{% url 'inventory:transfer_send' transfer.pk %}" class="btn btn-info">
                    <i class="bi bi-send"></i> Expédier
                </a>
                {% endif %}
                
                {% if transfer.status == 'sent' %}
                <a href="{% url 'inventory:transfer_receive' transfer.pk %}" class="btn btn-warning">
                    <i class="bi bi-inbox"></i> Réceptionner
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations du transfert -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations du transfert</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Numéro :</dt>
                            <dd class="col-sm-7"><code>{{ transfer.number }}</code></dd>
                            
                            <dt class="col-sm-5">Statut :</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-{% if transfer.status == 'pending' %}warning{% elif transfer.status == 'approved' %}info{% elif transfer.status == 'sent' %}primary{% elif transfer.status == 'received' %}success{% else %}secondary{% endif %} fs-6">
                                    {{ transfer.get_status_display }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-5">De :</dt>
                            <dd class="col-sm-7">
                                <a href="{% url 'inventory:warehouse_detail' transfer.from_warehouse.pk %}">
                                    <strong>{{ transfer.from_warehouse.name }}</strong>
                                </a>
                                <br>
                                <small class="text-muted">{{ transfer.from_warehouse.code }}</small>
                            </dd>
                            
                            <dt class="col-sm-5">Vers :</dt>
                            <dd class="col-sm-7">
                                <a href="{% url 'inventory:warehouse_detail' transfer.to_warehouse.pk %}">
                                    <strong>{{ transfer.to_warehouse.name }}</strong>
                                </a>
                                <br>
                                <small class="text-muted">{{ transfer.to_warehouse.code }}</small>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Date transfert :</dt>
                            <dd class="col-sm-7">{{ transfer.transfer_date|date:"d/m/Y" }}</dd>
                            
                            <dt class="col-sm-5">Créé par :</dt>
                            <dd class="col-sm-7">
                                <strong>{{ transfer.created_by.get_full_name|default:transfer.created_by.username }}</strong>
                                <br>
                                <small class="text-muted">{{ transfer.created_by.get_role_display }}</small>
                            </dd>
                            
                            <dt class="col-sm-5">Créé le :</dt>
                            <dd class="col-sm-7">{{ transfer.created_at|date:"d/m/Y à H:i" }}</dd>
                            
                            <dt class="col-sm-5">Modifié le :</dt>
                            <dd class="col-sm-7">{{ transfer.updated_at|date:"d/m/Y à H:i" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if transfer.notes %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6><i class="bi bi-chat-text"></i> Notes :</h6>
                        <p class="text-muted">{{ transfer.notes|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Produits du transfert -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-list-ul"></i> Produits transférés</h5>
                <span class="badge bg-primary fs-6">{{ lines|length }} produit{{ lines|length|pluralize }}</span>
            </div>
            <div class="card-body">
                {% if lines %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Référence</th>
                                <th>Quantité demandée</th>
                                <th>Unité</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in lines %}
                            <tr>
                                <td>
                                    <a href="{% url 'inventory:product_detail' line.product.pk %}">
                                        <strong>{{ line.product.name }}</strong>
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ line.product.category.name }}</small>
                                </td>
                                <td><code>{{ line.product.reference }}</code></td>
                                <td>
                                    <strong class="text-primary">{{ line.quantity_requested }}</strong>
                                </td>
                                <td>{{ line.product.unit }}</td>
                                <td>
                                    {% if transfer.status == 'received' %}
                                        <span class="badge bg-success">Transféré</span>
                                    {% elif transfer.status == 'sent' %}
                                        <span class="badge bg-primary">En transit</span>
                                    {% elif transfer.status == 'approved' %}
                                        <span class="badge bg-info">Approuvé</span>
                                    {% else %}
                                        <span class="badge bg-warning">En attente</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Résumé des quantités -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded">
                            <h6 class="text-primary">{{ lines|length }}</h6>
                            <small class="text-muted">Produits différents</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded">
                            <h6 class="text-info">
                                {% for line in lines %}{{ line.quantity_requested }}{% if not forloop.last %} + {% endif %}{% endfor %}
                            </h6>
                            <small class="text-muted">Quantité totale</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-light rounded">
                            <h6 class="text-success">{{ transfer.transfer_date|date:"d/m/Y" }}</h6>
                            <small class="text-muted">Date de transfert</small>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-list-ul display-4 text-muted"></i>
                    <h5 class="mt-3">Aucun produit</h5>
                    <p class="text-muted">Ce transfert ne contient aucun produit.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Historique du transfert -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Historique</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Transfert créé</h6>
                            <p class="timeline-text">
                                Par {{ transfer.created_by.get_full_name|default:transfer.created_by.username }}
                            </p>
                            <small class="text-muted">{{ transfer.created_at|date:"d/m/Y à H:i" }}</small>
                        </div>
                    </div>
                    
                    {% if transfer.status != 'pending' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Transfert approuvé</h6>
                            <p class="timeline-text">Approuvé par le magasinier central</p>
                            <small class="text-muted">{{ transfer.updated_at|date:"d/m/Y à H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if transfer.status == 'sent' or transfer.status == 'received' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Transfert expédié</h6>
                            <p class="timeline-text">Produits expédiés vers {{ transfer.to_warehouse.name }}</p>
                            <small class="text-muted">{{ transfer.updated_at|date:"d/m/Y à H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if transfer.status == 'received' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Transfert réceptionné</h6>
                            <p class="timeline-text">Produits réceptionnés dans {{ transfer.to_warehouse.name }}</p>
                            <small class="text-muted">{{ transfer.updated_at|date:"d/m/Y à H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Résumé du transfert -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clipboard-data"></i> Résumé</h6>
            </div>
            <div class="card-body text-center">
                <div class="transfer-icon mx-auto mb-3">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
                <h5>{{ transfer.number }}</h5>
                <p class="text-muted">{{ transfer.get_status_display }}</p>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary">{{ lines|length }}</h6>
                        <small class="text-muted">Produits</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">{{ transfer.transfer_date|date:"d/m" }}</h6>
                        <small class="text-muted">Date transfert</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions selon le statut -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if transfer.status == 'pending' and user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:transfer_edit' transfer.pk %}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> Modifier
                    </a>
                    <a href="{% url 'inventory:transfer_approve' transfer.pk %}" class="btn btn-success">
                        <i class="bi bi-check-circle"></i> Approuver
                    </a>
                    {% endif %}
                    
                    {% if transfer.status == 'approved' and user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:transfer_send' transfer.pk %}" class="btn btn-info">
                        <i class="bi bi-send"></i> Expédier
                    </a>
                    {% endif %}
                    
                    {% if transfer.status == 'sent' %}
                    <a href="{% url 'inventory:transfer_receive' transfer.pk %}" class="btn btn-warning">
                        <i class="bi bi-inbox"></i> Réceptionner
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'inventory:transfer_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-list"></i> Tous les transferts
                    </a>
                </div>
            </div>
        </div>

        <!-- Informations magasins -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-building"></i> Magasins</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">Source</h6>
                    <p class="mb-1">
                        <strong>{{ transfer.from_warehouse.name }}</strong>
                    </p>
                    <small class="text-muted">{{ transfer.from_warehouse.code }}</small>
                    {% if transfer.from_warehouse.manager %}
                    <br>
                    <small class="text-muted">
                        Gérant: {{ transfer.from_warehouse.manager.get_full_name|default:transfer.from_warehouse.manager.username }}
                    </small>
                    {% endif %}
                </div>
                
                <div class="text-center mb-3">
                    <i class="bi bi-arrow-down text-muted"></i>
                </div>
                
                <div>
                    <h6 class="text-success">Destination</h6>
                    <p class="mb-1">
                        <strong>{{ transfer.to_warehouse.name }}</strong>
                    </p>
                    <small class="text-muted">{{ transfer.to_warehouse.code }}</small>
                    {% if transfer.to_warehouse.manager %}
                    <br>
                    <small class="text-muted">
                        Gérant: {{ transfer.to_warehouse.manager.get_full_name|default:transfer.to_warehouse.manager.username }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.transfer-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 0.85rem;
}
</style>
{% endblock %}
