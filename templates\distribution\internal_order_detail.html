{% extends 'base.html' %}

{% block title %}BCN {{ order.number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'distribution:internal_order_list' %}">Commandes internes</a></li>
                <li class="breadcrumb-item active">{{ order.number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-clipboard-check"></i> Commande interne {{ order.number }}
                {% if order.status == 'draft' %}
                    <span class="badge bg-secondary ms-2">Brouillon</span>
                {% elif order.status == 'pending_approval' %}
                    <span class="badge bg-warning ms-2">En attente</span>
                {% elif order.status == 'approved' %}
                    <span class="badge bg-success ms-2">Approuvée</span>
                {% elif order.status == 'rejected' %}
                    <span class="badge bg-danger ms-2">Rejetée</span>
                {% elif order.status == 'partially_delivered' %}
                    <span class="badge bg-info ms-2">Partiellement livrée</span>
                {% elif order.status == 'fully_delivered' %}
                    <span class="badge bg-success ms-2">Livrée</span>
                {% elif order.status == 'cancelled' %}
                    <span class="badge bg-dark ms-2">Annulée</span>
                {% endif %}
            </h1>
            <div>
                <a href="{% url 'distribution:internal_order_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
                {% if order.status == 'draft' and order.created_by == user %}
                <a href="{% url 'distribution:internal_order_edit' order.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                {% endif %}
                {% if order.status == 'pending_approval' and user.role == 'manager' %}
                <a href="{% url 'distribution:internal_order_approve' order.pk %}" class="btn btn-success">
                    <i class="bi bi-check-circle"></i> Approuver
                </a>
                {% endif %}
                {% if order.status == 'approved' and user.can_manage_central_warehouse %}
                <a href="{% url 'distribution:delivery_note_create' %}?internal_order={{ order.pk }}" class="btn btn-info">
                    <i class="bi bi-truck"></i> Créer BDL
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Informations générales -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Numéro :</dt>
                            <dd class="col-sm-7"><strong>{{ order.number }}</strong></dd>
                            
                            <dt class="col-sm-5">Service demandeur :</dt>
                            <dd class="col-sm-7">{{ order.requesting_service.name }}</dd>
                            
                            <dt class="col-sm-5">Manager :</dt>
                            <dd class="col-sm-7">{{ order.requesting_service.manager.get_full_name }}</dd>
                            
                            <dt class="col-sm-5">Priorité :</dt>
                            <dd class="col-sm-7">
                                {% if order.priority == 'urgent' %}
                                    <span class="badge bg-danger">Urgent</span>
                                {% elif order.priority == 'high' %}
                                    <span class="badge bg-warning">Élevée</span>
                                {% elif order.priority == 'normal' %}
                                    <span class="badge bg-info">Normale</span>
                                {% else %}
                                    <span class="badge bg-secondary">Faible</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Date création :</dt>
                            <dd class="col-sm-7">{{ order.created_at|date:"d/m/Y H:i" }}</dd>
                            
                            <dt class="col-sm-5">Livraison souhaitée :</dt>
                            <dd class="col-sm-7">{{ order.requested_delivery_date|date:"d/m/Y"|default:"-" }}</dd>
                            
                            <dt class="col-sm-5">Créé par :</dt>
                            <dd class="col-sm-7">{{ order.created_by.get_full_name }}</dd>
                            
                            {% if order.approved_by %}
                            <dt class="col-sm-5">Approuvé par :</dt>
                            <dd class="col-sm-7">{{ order.approved_by.get_full_name }}</dd>
                            {% endif %}
                        </dl>
                    </div>
                </div>
                
                {% if order.justification %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Justification :</h6>
                        <p class="text-muted">{{ order.justification|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if order.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes :</h6>
                        <p class="text-muted">{{ order.notes|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if order.approval_notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes d'approbation :</h6>
                        <div class="alert alert-{% if order.status == 'approved' %}success{% else %}danger{% endif %}">
                            {{ order.approval_notes|linebreaks }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Lignes de commande -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list"></i> Produits demandés</h5>
            </div>
            <div class="card-body">
                {% if lines %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Qté demandée</th>
                                <th>Qté approuvée</th>
                                <th>Qté livrée</th>
                                <th>Qté restante</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in lines %}
                            <tr>
                                <td>
                                    <strong>{{ line.product.reference }}</strong><br>
                                    <small class="text-muted">{{ line.product.name }}</small>
                                </td>
                                <td>{{ line.quantity_requested }} {{ line.product.unit }}</td>
                                <td>
                                    {% if line.quantity_approved %}
                                        <span class="badge bg-success">{{ line.quantity_approved }} {{ line.product.unit }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if line.quantity_delivered > 0 %}
                                        <span class="badge bg-info">{{ line.quantity_delivered }} {{ line.product.unit }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if line.quantity_remaining > 0 %}
                                        <span class="badge bg-warning">{{ line.quantity_remaining }} {{ line.product.unit }}</span>
                                    {% elif line.quantity_approved %}
                                        <span class="badge bg-success">Complet</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if line.notes %}
                                        <small class="text-muted">{{ line.notes }}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center py-3">Aucun produit dans cette commande.</p>
                {% endif %}
            </div>
        </div>

        <!-- Bons de livraison associés -->
        {% if delivery_notes %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-truck"></i> Bons de livraison (BDL)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Créé par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for delivery_note in delivery_notes %}
                            <tr>
                                <td>
                                    <a href="{% url 'distribution:delivery_note_detail' delivery_note.pk %}">
                                        {{ delivery_note.number }}
                                    </a>
                                </td>
                                <td>{{ delivery_note.delivery_date|date:"d/m/Y" }}</td>
                                <td>
                                    {% if delivery_note.status == 'draft' %}
                                        <span class="badge bg-warning">Brouillon</span>
                                    {% elif delivery_note.status == 'prepared' %}
                                        <span class="badge bg-info">Préparé</span>
                                    {% elif delivery_note.status == 'delivered' %}
                                        <span class="badge bg-success">Livré</span>
                                    {% endif %}
                                </td>
                                <td>{{ delivery_note.created_by.get_full_name }}</td>
                                <td>
                                    <a href="{% url 'distribution:delivery_note_detail' delivery_note.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Résumé -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-graph-up"></i> Résumé</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ lines.count }}</h4>
                        <small class="text-muted">Produits</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ delivery_notes.count }}</h4>
                        <small class="text-muted">BDL créés</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-12">
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {% widthratio order.delivery_progress 1 100 %}%">
                                {% widthratio order.delivery_progress 1 100 %}%
                            </div>
                        </div>
                        <small class="text-muted">Progression livraison</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timeline -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clock-history"></i> Historique</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Commande créée</h6>
                            <p class="timeline-text">
                                {{ order.created_at|date:"d/m/Y H:i" }}<br>
                                <small class="text-muted">par {{ order.created_by.get_full_name }}</small>
                            </p>
                        </div>
                    </div>
                    
                    {% if order.status != 'draft' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Soumise pour approbation</h6>
                            <p class="timeline-text">
                                {{ order.updated_at|date:"d/m/Y H:i" }}
                            </p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if order.approved_by %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{% if order.status == 'approved' %}success{% else %}danger{% endif %}"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">
                                {% if order.status == 'approved' %}Approuvée{% else %}Rejetée{% endif %}
                            </h6>
                            <p class="timeline-text">
                                {{ order.updated_at|date:"d/m/Y H:i" }}<br>
                                <small class="text-muted">par {{ order.approved_by.get_full_name }}</small>
                            </p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% for delivery_note in delivery_notes %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">BDL {{ delivery_note.number }}</h6>
                            <p class="timeline-text">
                                {{ delivery_note.created_at|date:"d/m/Y H:i" }}<br>
                                <small class="text-muted">{{ delivery_note.status|capfirst }}</small>
                            </p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.timeline-text {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
}
</style>
{% endblock %}
