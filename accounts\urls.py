from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentification
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', views.CustomLogoutView.as_view(), name='logout'),

    # Profil utilisateur
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.user_profile_edit_view, name='profile_edit'),

    # Gestion des utilisateurs (CRUD)
    path('users/', views.user_list_view, name='user_list'),
    path('users/create/', views.UserCreateView.as_view(), name='user_create'),
    path('users/<int:pk>/', views.user_detail_view, name='user_detail'),
    path('users/<int:pk>/edit/', views.UserUpdateView.as_view(), name='user_edit'),
    path('users/<int:pk>/delete/', views.user_delete_view, name='user_delete'),
    path('users/<int:pk>/activate/', views.user_activate_view, name='user_activate'),
]
