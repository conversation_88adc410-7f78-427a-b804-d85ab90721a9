# Application de Gestion des Produits de Nettoyage (GPN)

## Description
Application web complète développée en Django pour la gestion des produits de nettoyage dans un environnement organisationnel structuré avec entrepôts et services internes.

## Fonctionnalités Implémentées

### ✅ 1. Système d'authentification et permissions
- **5 rôles utilisateurs** : <PERSON><PERSON>, Manager, Magasinier central, <PERSON><PERSON><PERSON> sous-magasin, Service demandeur
- **Permissions granulaires** par rôle avec décorateurs personnalisés
- **Interface d'administration** Django personnalisée

### ✅ 2. Gestion des approvisionnements
- **Gestion des fournisseurs** : CRUD complet avec recherche et pagination
- **Contrats fournisseurs** : Suivi des contrats avec dates et montants
- **Bons de commande (BC)** : Création, modification, suivi des statuts
- **Lignes de commande** : Gestion détaillée des produits commandés

### ✅ 3. Système de réception des produits
- **Réceptions partielles/totales** : Gestion flexible des livraisons
- **Validation des réceptions** : Mise à jour automatique des stocks
- **Traçabilité complète** : Historique des mouvements de stock
- **Documents de livraison** : Stockage des numéros de BL

### ✅ 4. Gestion des magasins et transferts
- **Architecture hiérarchique** : Magasin central et sous-magasins
- **Transferts inter-magasins** : Workflow d'approbation et traçabilité
- **Gestion des stocks** : Vue d'ensemble avec alertes de stock faible
- **Ajustements de stock** : Corrections manuelles avec justification

### ✅ 5. Système de distribution interne
- **Services internes** : Gestion des départements demandeurs
- **Commandes internes (BCN)** : Demandes de produits par les services
- **Workflow d'approbation** : Validation multi-niveaux
- **Bons de livraison (BDL)** : Préparation et livraison des commandes

### ✅ 6. Gestion documentaire
- **Stockage BLOB** : Documents PDF/images stockés en base
- **Association flexible** : Documents liés aux entités métier
- **Types de documents** : Catégorisation et recherche
- **Téléchargement sécurisé** : Contrôle d'accès aux documents

### ✅ 7. Interface utilisateur responsive
- **Bootstrap 5** : Design moderne et responsive
- **Navigation contextuelle** : Menus adaptés aux rôles
- **Formulaires ergonomiques** : Validation côté client et serveur
- **Pagination et recherche** : Navigation efficace dans les données

## Architecture Technique

### Backend
- **Django 5.2.6** : Framework web Python
- **SQLite** : Base de données (configurable pour MySQL)
- **Architecture modulaire** : 6 applications Django distinctes

### Frontend
- **Django Templates** : Moteur de templates intégré
- **Bootstrap 5** : Framework CSS responsive
- **Bootstrap Icons** : Iconographie cohérente
- **JavaScript vanilla** : Interactions côté client

### Applications Django
1. **accounts** : Authentification et gestion des utilisateurs
2. **inventory** : Gestion des produits, magasins et stocks
3. **procurement** : Approvisionnements et fournisseurs
4. **distribution** : Distribution interne et services
5. **documents** : Gestion documentaire et réceptions
6. **dashboard** : Tableaux de bord et statistiques

## Modèles de Données Principaux

### Utilisateurs et Permissions
- `User` : Utilisateur personnalisé avec rôles
- Permissions basées sur les rôles métier

### Inventaire
- `Product` : Produits avec catégories et références
- `Warehouse` : Magasins centraux et sous-magasins
- `Stock` : Quantités par produit et magasin
- `StockTransfer` : Transferts entre magasins

### Approvisionnement
- `Supplier` : Fournisseurs avec contacts
- `Contract` : Contrats fournisseurs
- `PurchaseOrder` : Bons de commande
- `PurchaseOrderLine` : Lignes de commande

### Distribution
- `InternalService` : Services internes
- `InternalOrder` : Commandes internes (BCN)
- `DeliveryNote` : Bons de livraison (BDL)

### Documents
- `Document` : Stockage BLOB des fichiers
- `Reception` : Réceptions de marchandises
- `StockMovement` : Traçabilité des mouvements

## Installation et Configuration

### Prérequis
- Python 3.8+
- pip (gestionnaire de paquets Python)

### Installation
```bash
# Cloner le projet
git clone <repository-url>
cd cleaning_management

# Créer un environnement virtuel
python -m venv venv
venv\Scripts\activate  # Windows
# ou source venv/bin/activate  # Linux/Mac

# Installer les dépendances
pip install django pillow reportlab openpyxl django-crispy-forms crispy-bootstrap5 django-extensions

# Configurer la base de données
python manage.py makemigrations
python manage.py migrate

# Créer un superutilisateur
python manage.py createsuperuser

# Créer des données de test
python create_test_data.py

# Lancer le serveur
python manage.py runserver
```

### Comptes de Test
Après exécution du script `create_test_data.py` :
- **admin** / admin123 (Administrateur)
- **manager** / manager123 (Manager)
- **magasinier** / magasinier123 (Magasinier central)
- **gerant** / gerant123 (Gérant sous-magasin)
- **demandeur** / demandeur123 (Service demandeur)

## Fonctionnalités Avancées

### Workflow Métier
1. **Approvisionnement** : Fournisseur → Contrat → BC → Réception → Stock
2. **Distribution** : Service → BCN → Approbation → BDL → Livraison
3. **Transferts** : Demande → Approbation → Expédition → Réception

### Traçabilité
- **Mouvements de stock** : Historique complet des entrées/sorties
- **Références croisées** : Liens entre BC, réceptions, transferts
- **Audit trail** : Qui, quand, pourquoi pour chaque action

### Sécurité
- **Authentification** : Login/logout sécurisé
- **Autorisation** : Contrôle d'accès par rôle
- **Validation** : Contrôles métier et techniques
- **Journalisation** : Logs des actions importantes

## Développements Futurs

### Fonctionnalités à Implémenter
- [ ] Tableaux de bord avec graphiques (Chart.js)
- [ ] Statistiques paramétrables avec filtres
- [ ] Export Excel/PDF des rapports
- [ ] Alertes automatiques (stock faible, commandes urgentes)
- [ ] API REST pour intégrations externes
- [ ] Tests unitaires et d'intégration
- [ ] Déploiement en production

### Améliorations Techniques
- [ ] Cache Redis pour les performances
- [ ] Celery pour les tâches asynchrones
- [ ] Docker pour le déploiement
- [ ] CI/CD avec GitHub Actions
- [ ] Monitoring avec Sentry

## Support et Maintenance

### Structure du Code
- **Modèles** : Logique métier dans les models.py
- **Vues** : Contrôleurs dans les views.py
- **Templates** : Interface utilisateur dans templates/
- **Formulaires** : Validation dans forms.py
- **URLs** : Routage dans urls.py

### Bonnes Pratiques
- **DRY** : Don't Repeat Yourself
- **Séparation des responsabilités** : Modèles, vues, templates
- **Validation** : Côté client et serveur
- **Sécurité** : CSRF, permissions, validation des entrées
- **Performance** : Requêtes optimisées, pagination

---

**Développé avec Django 5.2.6 et Bootstrap 5**  
**Application complète de gestion des produits de nettoyage**
