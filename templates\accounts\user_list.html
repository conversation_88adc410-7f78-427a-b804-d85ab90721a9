{% extends 'base.html' %}

{% block title %}Gestion des utilisateurs - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-people"></i> Gestion des utilisateurs</h1>
            {% if user.role == 'admin' %}
            <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Nouvel utilisateur
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" 
                               placeholder="Rechercher par nom, email, département..."
                               value="{{ search_query }}">
                    </div>
                    <div class="col-md-3">
                        <select name="role" class="form-select">
                            <option value="">Tous les rôles</option>
                            {% for role_value, role_label in role_choices %}
                            <option value="{{ role_value }}" {% if role_filter == role_value %}selected{% endif %}>
                                {{ role_label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">Tous les statuts</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Actifs</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactifs</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i> Rechercher
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_users }}</h4>
                        <p class="mb-0">Total utilisateurs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ active_users }}</h4>
                        <p class="mb-0">Utilisateurs actifs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ inactive_users }}</h4>
                        <p class="mb-0">Utilisateurs inactifs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-x display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des utilisateurs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Utilisateur</th>
                                <th>Email</th>
                                <th>Rôle</th>
                                <th>Département</th>
                                <th>Téléphone</th>
                                <th>Statut</th>
                                <th>Dernière connexion</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user_item in page_obj %}
                            <tr class="{% if not user_item.is_active %}table-secondary{% endif %}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            <i class="bi bi-person-fill"></i>
                                        </div>
                                        <div>
                                            <strong>{{ user_item.get_full_name|default:user_item.username }}</strong>
                                            <br>
                                            <small class="text-muted">@{{ user_item.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if user_item.email %}
                                        <a href="mailto:{{ user_item.email }}">{{ user_item.email }}</a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{% if user_item.role == 'admin' %}danger{% elif user_item.role == 'manager' %}warning{% elif user_item.role == 'central_storekeeper' %}primary{% elif user_item.role == 'sub_storekeeper' %}info{% else %}secondary{% endif %}">
                                        {{ user_item.get_role_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if user_item.department %}
                                        {{ user_item.department }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user_item.phone %}
                                        <a href="tel:{{ user_item.phone }}">{{ user_item.phone }}</a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user_item.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user_item.last_login %}
                                        <small class="text-muted">
                                            {{ user_item.last_login|date:"d/m/Y H:i" }}
                                        </small>
                                    {% else %}
                                        <span class="text-muted">Jamais</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'accounts:user_detail' user_item.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="Voir">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        
                                        {% if user.role == 'admin' or user.role == 'manager' %}
                                        <a href="{% url 'accounts:user_edit' user_item.pk %}" 
                                           class="btn btn-sm btn-outline-secondary" title="Modifier">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% endif %}
                                        
                                        {% if user.role == 'admin' and user_item != user %}
                                            {% if user_item.is_active %}
                                            <a href="{% url 'accounts:user_delete' user_item.pk %}" 
                                               class="btn btn-sm btn-outline-danger" title="Désactiver">
                                                <i class="bi bi-person-x"></i>
                                            </a>
                                            {% else %}
                                            <a href="{% url 'accounts:user_activate' user_item.pk %}" 
                                               class="btn btn-sm btn-outline-success" title="Réactiver">
                                                <i class="bi bi-person-check"></i>
                                            </a>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1&search={{ search_query }}&role={{ role_filter }}&status={{ status_filter }}">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&role={{ role_filter }}&status={{ status_filter }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&role={{ role_filter }}&status={{ status_filter }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&search={{ search_query }}&role={{ role_filter }}&status={{ status_filter }}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun utilisateur trouvé</h3>
                    <p class="text-muted">
                        {% if search_query or role_filter or status_filter %}
                            Aucun utilisateur ne correspond aux critères de recherche.
                        {% else %}
                            Aucun utilisateur dans le système.
                        {% endif %}
                    </p>
                    {% if user.role == 'admin' %}
                    <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Créer le premier utilisateur
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}
</style>
{% endblock %}
