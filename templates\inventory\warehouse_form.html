{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Modifier{% else %}Nouveau{% endif %} magasin - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">Magasins</a></li>
                {% if form.instance.pk %}
                <li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_detail' form.instance.pk %}">{{ form.instance.name }}</a></li>
                <li class="breadcrumb-item active">Modifier</li>
                {% else %}
                <li class="breadcrumb-item active">Nouveau magasin</li>
                {% endif %}
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-building"></i>
            {% if form.instance.pk %}
                Modifier le magasin
            {% else %}
                Nouveau magasin
            {% endif %}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- Informations de base -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations de base</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    {{ form.code.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.code.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Code unique du magasin (ex: MAG001)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.warehouse_type.id_for_label }}" class="form-label">
                                    {{ form.warehouse_type.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.warehouse_type }}
                                {% if form.warehouse_type.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.warehouse_type.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Central = magasin principal, Sous-magasin = dépendant d'un central
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.parent_warehouse.id_for_label }}" class="form-label">
                                    {{ form.parent_warehouse.label }}
                                </label>
                                {{ form.parent_warehouse }}
                                {% if form.parent_warehouse.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.parent_warehouse.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Obligatoire pour les sous-magasins
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.address.errors.0 }}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Adresse complète du magasin
                        </small>
                    </div>
                </div>
            </div>

            <!-- Gestion et permissions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-person-badge"></i> Gestion et permissions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.manager.id_for_label }}" class="form-label">
                                    {{ form.manager.label }}
                                </label>
                                {{ form.manager }}
                                {% if form.manager.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.manager.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Utilisateur responsable de ce magasin
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.is_active.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Décocher pour désactiver le magasin
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Erreurs générales du formulaire -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
            
            <div class="d-flex justify-content-between">
                <a href="{% if form.instance.pk %}{% url 'inventory:warehouse_detail' form.instance.pk %}{% else %}{% url 'inventory:warehouse_list' %}{% endif %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Annuler
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i> 
                    {% if form.instance.pk %}Modifier{% else %}Créer{% endif %}
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                {% if form.instance.pk %}
                <h6>Modification de magasin :</h6>
                <ul class="small">
                    <li>Modifiez les informations nécessaires</li>
                    <li>Le code doit rester unique</li>
                    <li>Vérifiez le type et le parent</li>
                    <li>Assignez un gérant approprié</li>
                </ul>
                {% else %}
                <h6>Création de magasin :</h6>
                <ul class="small">
                    <li>Tous les champs marqués * sont obligatoires</li>
                    <li>Le code doit être unique</li>
                    <li>Choisissez le bon type de magasin</li>
                    <li>Assignez un gérant qualifié</li>
                </ul>
                {% endif %}
                
                <hr>
                
                <h6>Types de magasins :</h6>
                <ul class="small text-muted">
                    <li><strong>Central :</strong> Magasin principal, peut avoir des sous-magasins</li>
                    <li><strong>Sous-magasin :</strong> Dépend d'un magasin central</li>
                </ul>
                
                <hr>
                
                <h6>Rôles des gérants :</h6>
                <ul class="small text-muted">
                    <li><strong>Magasinier Central :</strong> Gère les magasins centraux</li>
                    <li><strong>Gérant Sous-Magasin :</strong> Gère les sous-magasins</li>
                </ul>
            </div>
        </div>
        
        {% if form.instance.pk %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-graph-up"></i> Informations magasin</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-2">
                    <strong>Code :</strong> {{ form.instance.code }}
                </p>
                <p class="small text-muted mb-2">
                    <strong>Type :</strong> {{ form.instance.get_warehouse_type_display }}
                </p>
                <p class="small text-muted mb-2">
                    <strong>Créé le :</strong> {{ form.instance.created_at|date:"d/m/Y" }}
                </p>
                <p class="small text-muted mb-0">
                    <strong>Modifié le :</strong> {{ form.instance.updated_at|date:"d/m/Y" }}
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const warehouseTypeField = document.getElementById('{{ form.warehouse_type.id_for_label }}');
    const parentWarehouseField = document.getElementById('{{ form.parent_warehouse.id_for_label }}');
    const parentWarehouseGroup = parentWarehouseField.closest('.mb-3');
    
    function toggleParentWarehouse() {
        if (warehouseTypeField.value === 'sub') {
            parentWarehouseGroup.style.display = 'block';
            parentWarehouseField.required = true;
        } else {
            parentWarehouseGroup.style.display = 'none';
            parentWarehouseField.required = false;
            parentWarehouseField.value = '';
        }
    }
    
    warehouseTypeField.addEventListener('change', toggleParentWarehouse);
    toggleParentWarehouse(); // Initial call
});
</script>
{% endblock %}
