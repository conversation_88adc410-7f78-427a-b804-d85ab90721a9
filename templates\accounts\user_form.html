{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title|default:"Utilisateur" }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                {% if request.resolver_match.url_name == 'profile_edit' %}
                <li class="breadcrumb-item"><a href="{% url 'accounts:profile' %}">Mon profil</a></li>
                <li class="breadcrumb-item active">Modifier</li>
                {% else %}
                <li class="breadcrumb-item"><a href="{% url 'accounts:user_list' %}">Utilisateurs</a></li>
                <li class="breadcrumb-item active">{{ title|default:"Utilisateur" }}</li>
                {% endif %}
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-person{% if action == 'Modifier' %}-gear{% else %}-plus{% endif %}"></i>
            {{ title|default:"Nouvel utilisateur" }}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- Informations de connexion -->
            {% if 'username' in form.fields %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-key"></i> Informations de connexion</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    {{ form.username.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Nom d'utilisateur unique pour la connexion
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    {% if 'password1' in form.fields %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">
                                    {{ form.password1.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password1.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">
                                    {{ form.password2.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password2.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Informations personnelles -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-person"></i> Informations personnelles</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    {{ form.first_name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    {{ form.last_name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    {{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.department.id_for_label }}" class="form-label">
                            {{ form.department.label }}
                        </label>
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.department.errors.0 }}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Service ou département de rattachement
                        </small>
                    </div>
                </div>
            </div>

            <!-- Rôle et permissions -->
            {% if 'role' in form.fields or 'is_active' in form.fields %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-shield-check"></i> Rôle et permissions</h5>
                </div>
                <div class="card-body">
                    {% if 'role' in form.fields %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.role.id_for_label }}" class="form-label">
                                    {{ form.role.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.role }}
                                {% if form.role.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.role.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        {% if 'is_active' in form.fields %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.is_active.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Décocher pour désactiver l'utilisateur
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- Description des rôles -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Description des rôles :</h6>
                        <ul class="mb-0 small">
                            <li><strong>Administrateur :</strong> Accès complet au système</li>
                            <li><strong>Manager :</strong> Gestion et approbation des demandes</li>
                            <li><strong>Magasinier Central :</strong> Gestion du stock central</li>
                            <li><strong>Gérant Sous-Magasin :</strong> Gestion d'un sous-magasin</li>
                            <li><strong>Service Demandeur :</strong> Demande de produits uniquement</li>
                        </ul>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Erreurs générales du formulaire -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
            
            <div class="d-flex justify-content-between">
                <a href="{% if request.resolver_match.url_name == 'profile_edit' %}{% url 'accounts:profile' %}{% else %}{% url 'accounts:user_list' %}{% endif %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Annuler
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i> {{ action|default:"Créer" }}
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                {% if action == 'Modifier' %}
                <h6>Modification d'utilisateur :</h6>
                <ul class="small">
                    <li>Modifiez les informations nécessaires</li>
                    <li>Le nom d'utilisateur ne peut pas être changé</li>
                    <li>Laissez les mots de passe vides pour les conserver</li>
                    <li>Vérifiez le rôle et les permissions</li>
                </ul>
                {% else %}
                <h6>Création d'utilisateur :</h6>
                <ul class="small">
                    <li>Tous les champs marqués * sont obligatoires</li>
                    <li>Le nom d'utilisateur doit être unique</li>
                    <li>L'email doit être valide</li>
                    <li>Choisissez le rôle approprié</li>
                </ul>
                {% endif %}
                
                <hr>
                
                <h6>Sécurité :</h6>
                <ul class="small text-muted">
                    <li>Utilisez des mots de passe forts</li>
                    <li>Vérifiez les permissions accordées</li>
                    <li>Désactivez plutôt que supprimer</li>
                    <li>Gardez les informations à jour</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
