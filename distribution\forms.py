from django import forms
from django.forms import inlineformset_factory
from .models import InternalService, InternalOrder, InternalOrderLine, DeliveryNote
from inventory.models import Product, Warehouse, Stock


class InternalServiceForm(forms.ModelForm):
    """Formulaire pour les services internes"""
    class Meta:
        model = InternalService
        fields = ['code', 'name', 'manager', 'description', 'is_active']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'manager': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les managers selon leur rôle
        from accounts.models import User
        self.fields['manager'].queryset = User.objects.filter(
            role__in=['manager', 'service_requester'],
            is_active=True
        )


class InternalOrderForm(forms.ModelForm):
    """Formulaire pour les commandes internes (BCN)"""
    class Meta:
        model = InternalOrder
        fields = ['number', 'requesting_service', 'needed_date', 'priority', 'justification', 'notes']
        widgets = {
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'requesting_service': forms.Select(attrs={'class': 'form-control'}),
            'needed_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'priority': forms.Select(attrs={'class': 'form-control'}),
            'justification': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filtrer les services selon les permissions de l'utilisateur
            if user.role == 'service_requester':
                # Un service demandeur ne peut commander que pour son service
                self.fields['requesting_service'].queryset = InternalService.objects.filter(
                    manager=user,
                    is_active=True
                )
            else:
                # Autres rôles : tous les services actifs
                self.fields['requesting_service'].queryset = InternalService.objects.filter(
                    is_active=True
                )


class InternalOrderLineForm(forms.ModelForm):
    """Formulaire pour les lignes de commande interne"""
    class Meta:
        model = InternalOrderLine
        fields = ['product', 'quantity_requested']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'quantity_requested': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limiter aux produits actifs
        self.fields['product'].queryset = Product.objects.filter(is_active=True)


# Formset pour les lignes de commande interne
InternalOrderLineFormSet = inlineformset_factory(
    InternalOrder,
    InternalOrderLine,
    form=InternalOrderLineForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class DeliveryNoteForm(forms.ModelForm):
    """Formulaire pour les bons de livraison (BDL)"""
    class Meta:
        model = DeliveryNote
        fields = ['number', 'internal_order', 'delivery_date', 'received_by_name', 'notes']
        widgets = {
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'internal_order': forms.Select(attrs={'class': 'form-control'}),
            'delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'received_by_name': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Filtrer les commandes internes approuvées
        self.fields['internal_order'].queryset = InternalOrder.objects.filter(
            status='approved'
        ).select_related('requesting_service')
        
        if user:
            # Filtrer les magasins selon les permissions de l'utilisateur
            if user.role == 'central_storekeeper':
                self.fields['warehouse'].queryset = Warehouse.objects.filter(
                    warehouse_type='central',
                    is_active=True
                )
            elif user.role == 'sub_storekeeper':
                self.fields['warehouse'].queryset = Warehouse.objects.filter(
                    manager=user,
                    is_active=True
                )
            else:
                self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)


# Note: DeliveryNoteLineForm et DeliveryNoteLineFormSet supprimés car DeliveryNoteLine n'existe pas dans le modèle


class InternalOrderSearchForm(forms.Form):
    """Formulaire de recherche pour les commandes internes"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par numéro...'
        })
    )
    requesting_service = forms.ModelChoiceField(
        queryset=InternalService.objects.filter(is_active=True),
        required=False,
        empty_label="Tous les services",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + InternalOrder.ORDER_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    priority = forms.ChoiceField(
        choices=[('', 'Toutes les priorités')] + InternalOrder.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class DeliveryNoteSearchForm(forms.Form):
    """Formulaire de recherche pour les bons de livraison"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Rechercher par numéro...'
        })
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        required=False,
        empty_label="Tous les magasins",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[('', 'Tous les statuts')] + DeliveryNote.DELIVERY_STATUS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class OrderApprovalForm(forms.Form):
    """Formulaire pour l'approbation des commandes"""
    approval_notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        required=False,
        label="Notes d'approbation"
    )
    action = forms.ChoiceField(
        choices=[
            ('approve', 'Approuver'),
            ('reject', 'Rejeter')
        ],
        widget=forms.RadioSelect(),
        label="Action"
    )
