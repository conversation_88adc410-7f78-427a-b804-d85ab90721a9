from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q, F
from django.core.paginator import Paginator
from django.db import transaction
from accounts.decorators import central_storekeeper_required, storekeeper_required
from .models import Document, StockMovement, Reception, ReceptionLine
from .forms import ReceptionForm, ReceptionLineFormSet, DocumentUploadForm, ReceptionSearchForm, StockAdjustmentForm
from inventory.models import Stock, Product, Warehouse
from procurement.models import PurchaseOrderLine


@login_required
@central_storekeeper_required
def reception_list(request):
    """Liste des réceptions"""
    form = ReceptionSearchForm(request.GET)
    receptions = Reception.objects.select_related(
        'purchase_order__supplier', 'warehouse', 'received_by'
    )

    if form.is_valid():
        search = form.cleaned_data.get('search')
        purchase_order = form.cleaned_data.get('purchase_order')
        status = form.cleaned_data.get('status')
        warehouse = form.cleaned_data.get('warehouse')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            receptions = receptions.filter(
                Q(number__icontains=search) |
                Q(purchase_order__number__icontains=search) |
                Q(delivery_note_number__icontains=search)
            )
        if purchase_order:
            receptions = receptions.filter(purchase_order=purchase_order)
        if status:
            receptions = receptions.filter(status=status)
        if warehouse:
            receptions = receptions.filter(warehouse=warehouse)
        if date_from:
            receptions = receptions.filter(reception_date__gte=date_from)
        if date_to:
            receptions = receptions.filter(reception_date__lte=date_to)

    receptions = receptions.order_by('-created_at')
    paginator = Paginator(receptions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'documents/reception_list.html', {
        'page_obj': page_obj,
        'form': form
    })


@login_required
@central_storekeeper_required
def reception_create(request):
    """Création d'une réception"""
    if request.method == 'POST':
        form = ReceptionForm(request.POST)

        if form.is_valid():
            reception = form.save(commit=False)
            reception.received_by = request.user
            reception.save()

            # Créer les lignes de réception basées sur le bon de commande
            purchase_order = reception.purchase_order
            for po_line in purchase_order.lines.all():
                ReceptionLine.objects.create(
                    reception=reception,
                    product=po_line.product,
                    quantity_received=0  # À remplir par l'utilisateur
                )

            messages.success(request, f'Réception "{reception.number}" créée avec succès.')
            return redirect('documents:reception_detail', pk=reception.pk)
    else:
        form = ReceptionForm()

    return render(request, 'documents/reception_form.html', {'form': form})


@login_required
@central_storekeeper_required
def reception_detail(request, pk):
    """Détail d'une réception"""
    reception = get_object_or_404(Reception, pk=pk)
    lines = reception.lines.select_related('product').all()

    # Calculer les totaux
    total_expected = sum(
        po_line.quantity_ordered - po_line.quantity_received
        for po_line in reception.purchase_order.lines.all()
    )
    total_received = sum(line.quantity_received for line in lines)

    return render(request, 'documents/reception_detail.html', {
        'reception': reception,
        'lines': lines,
        'total_expected': total_expected,
        'total_received': total_received
    })


@login_required
@central_storekeeper_required
def reception_validate(request, pk):
    """Validation d'une réception"""
    reception = get_object_or_404(Reception, pk=pk)

    if reception.status != 'draft':
        messages.error(request, 'Cette réception a déjà été validée.')
        return redirect('documents:reception_detail', pk=pk)

    if request.method == 'POST':
        formset = ReceptionLineFormSet(request.POST, instance=reception)

        if formset.is_valid():
            with transaction.atomic():
                formset.save()

                # Mettre à jour les stocks et les quantités reçues du BC
                for line in reception.lines.all():
                    if line.quantity_received > 0:
                        # Mettre à jour le stock
                        stock, created = Stock.objects.get_or_create(
                            warehouse=reception.warehouse,
                            product=line.product,
                            defaults={'quantity': 0}
                        )
                        stock.quantity += line.quantity_received
                        stock.save()

                        # Créer un mouvement de stock
                        StockMovement.objects.create(
                            movement_type='reception',
                            product=line.product,
                            warehouse=reception.warehouse,
                            quantity=line.quantity_received,
                            reference_number=reception.number,
                            content_object=reception,
                            created_by=request.user,
                            notes=f'Réception BC {reception.purchase_order.number}'
                        )

                        # Mettre à jour la quantité reçue du bon de commande
                        try:
                            po_line = PurchaseOrderLine.objects.get(
                                purchase_order=reception.purchase_order,
                                product=line.product
                            )
                            po_line.quantity_received += line.quantity_received
                            po_line.save()
                        except PurchaseOrderLine.DoesNotExist:
                            pass

                # Marquer la réception comme validée
                reception.status = 'validated'
                reception.save()

                # Mettre à jour le statut du bon de commande
                purchase_order = reception.purchase_order
                all_received = all(
                    po_line.is_fully_received
                    for po_line in purchase_order.lines.all()
                )
                if all_received:
                    purchase_order.status = 'fully_received'
                else:
                    purchase_order.status = 'partially_received'
                purchase_order.save()

                messages.success(request, f'Réception "{reception.number}" validée avec succès.')
                return redirect('documents:reception_detail', pk=pk)
    else:
        formset = ReceptionLineFormSet(instance=reception)

    return render(request, 'documents/reception_validate.html', {
        'reception': reception,
        'formset': formset
    })


@login_required
def document_list(request):
    """Liste des documents"""
    search = request.GET.get('search', '')
    document_type = request.GET.get('document_type', '')

    documents = Document.objects.select_related('uploaded_by')

    if search:
        documents = documents.filter(
            Q(name__icontains=search) |
            Q(file_name__icontains=search)
        )

    if document_type:
        documents = documents.filter(document_type=document_type)

    documents = documents.order_by('-uploaded_at')
    paginator = Paginator(documents, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'documents/document_list.html', {
        'page_obj': page_obj,
        'search': search,
        'document_type': document_type,
        'document_types': Document.DOCUMENT_TYPES
    })


@login_required
@storekeeper_required
def document_upload(request):
    """Upload d'un document"""
    if request.method == 'POST':
        form = DocumentUploadForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.uploaded_by = request.user
            document.save()

            messages.success(request, f'Document "{document.name}" uploadé avec succès.')
            return redirect('documents:document_detail', pk=document.pk)
    else:
        form = DocumentUploadForm()

    return render(request, 'documents/document_upload.html', {'form': form})


@login_required
def document_detail(request, pk):
    """Détail d'un document"""
    document = get_object_or_404(Document, pk=pk)
    return render(request, 'documents/document_detail.html', {'document': document})


@login_required
def document_download(request, pk):
    """Téléchargement d'un document"""
    document = get_object_or_404(Document, pk=pk)
    response = HttpResponse(document.file_data, content_type=document.mime_type)
    response['Content-Disposition'] = f'attachment; filename="{document.file_name}"'
    return response


@login_required
def stock_movement_list(request):
    """Liste des mouvements de stock"""
    search = request.GET.get('search', '')
    movement_type = request.GET.get('movement_type', '')
    warehouse_id = request.GET.get('warehouse', '')

    movements = StockMovement.objects.select_related(
        'product', 'warehouse', 'created_by'
    )

    if search:
        movements = movements.filter(
            Q(product__name__icontains=search) |
            Q(reference_number__icontains=search)
        )

    if movement_type:
        movements = movements.filter(movement_type=movement_type)

    if warehouse_id:
        movements = movements.filter(warehouse_id=warehouse_id)

    movements = movements.order_by('-created_at')
    paginator = Paginator(movements, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'documents/stock_movement_list.html', {
        'page_obj': page_obj,
        'search': search,
        'movement_type': movement_type,
        'warehouse_id': warehouse_id,
        'movement_types': StockMovement.MOVEMENT_TYPES,
        'warehouses': Warehouse.objects.filter(is_active=True)
    })


@login_required
@storekeeper_required
def stock_adjust(request):
    """Ajustement de stock"""
    if request.method == 'POST':
        form = StockAdjustmentForm(request.POST, user=request.user)
        if form.is_valid():
            warehouse = form.cleaned_data['warehouse']
            product = form.cleaned_data['product']
            adjustment_type = form.cleaned_data['adjustment_type']
            quantity = form.cleaned_data['quantity']
            reason = form.cleaned_data['reason']

            with transaction.atomic():
                # Obtenir ou créer le stock
                stock, created = Stock.objects.get_or_create(
                    warehouse=warehouse,
                    product=product,
                    defaults={'quantity': 0}
                )

                old_quantity = stock.quantity

                # Appliquer l'ajustement
                if adjustment_type == 'increase':
                    stock.quantity += quantity
                    movement_quantity = quantity
                elif adjustment_type == 'decrease':
                    stock.quantity = max(0, stock.quantity - quantity)
                    movement_quantity = -(old_quantity - stock.quantity)
                else:  # set
                    stock.quantity = quantity
                    movement_quantity = quantity - old_quantity

                stock.save()

                # Créer un mouvement de stock
                if movement_quantity != 0:
                    StockMovement.objects.create(
                        movement_type='adjustment',
                        product=product,
                        warehouse=warehouse,
                        quantity=movement_quantity,
                        reference_number=f'ADJ-{stock.pk}-{request.user.pk}',
                        notes=reason,
                        created_by=request.user
                    )

                messages.success(
                    request,
                    f'Stock ajusté pour {product.name} dans {warehouse.name}: '
                    f'{old_quantity} → {stock.quantity} {product.unit}'
                )
                return redirect('inventory:stock_list')
    else:
        form = StockAdjustmentForm(user=request.user)

    return render(request, 'documents/stock_adjust.html', {'form': form})
