{% extends 'base.html' %}

{% block title %}Réceptions - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-truck"></i> Réceptions</h1>
            <a href="{% url 'documents:reception_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouvelle réception
            </a>
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        {{ form.search }}
                    </div>
                    <div class="col-md-2">
                        {{ form.purchase_order }}
                    </div>
                    <div class="col-md-2">
                        {{ form.status }}
                    </div>
                    <div class="col-md-2">
                        {{ form.warehouse }}
                    </div>
                    <div class="col-md-2">
                        {{ form.date_from }}
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des réceptions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Bon de commande</th>
                                <th>Fournisseur</th>
                                <th>Magasin</th>
                                <th>Date réception</th>
                                <th>BL n°</th>
                                <th>Statut</th>
                                <th>Reçu par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for reception in page_obj %}
                            <tr>
                                <td><strong>{{ reception.number }}</strong></td>
                                <td>
                                    <a href="{% url 'procurement:purchase_order_detail' reception.purchase_order.pk %}">
                                        {{ reception.purchase_order.number }}
                                    </a>
                                </td>
                                <td>{{ reception.purchase_order.supplier.name }}</td>
                                <td>{{ reception.warehouse.name }}</td>
                                <td>{{ reception.reception_date|date:"d/m/Y" }}</td>
                                <td>{{ reception.delivery_note_number|default:"-" }}</td>
                                <td>
                                    {% if reception.status == 'draft' %}
                                        <span class="badge bg-warning">Brouillon</span>
                                    {% elif reception.status == 'validated' %}
                                        <span class="badge bg-success">Validée</span>
                                    {% elif reception.status == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                    {% endif %}
                                </td>
                                <td>{{ reception.received_by.get_full_name }}</td>
                                <td>
                                    <a href="{% url 'documents:reception_detail' reception.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    {% if reception.status == 'draft' %}
                                    <a href="{% url 'documents:reception_validate' reception.pk %}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-check-circle"></i> Valider
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-truck display-1 text-muted"></i>
                    <h3 class="mt-3">Aucune réception trouvée</h3>
                    <p class="text-muted">Commencez par créer votre première réception.</p>
                    <a href="{% url 'documents:reception_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouvelle réception
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <p class="mb-0">Total réceptions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-truck display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ draft_count|default:0 }}</h4>
                        <p class="mb-0">En attente</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ validated_count|default:0 }}</h4>
                        <p class="mb-0">Validées</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ this_month_count|default:0 }}</h4>
                        <p class="mb-0">Ce mois</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
