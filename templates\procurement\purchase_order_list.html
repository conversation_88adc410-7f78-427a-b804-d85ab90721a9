{% extends 'base.html' %}

{% block title %}Bons de commande - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-cart"></i> Bons de commande</h1>
            {% if user.can_manage_central_warehouse %}
            <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau BC
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        {{ form.search }}
                    </div>
                    <div class="col-md-2">
                        {{ form.supplier }}
                    </div>
                    <div class="col-md-2">
                        {{ form.status }}
                    </div>
                    <div class="col-md-2">
                        {{ form.date_from }}
                    </div>
                    <div class="col-md-2">
                        {{ form.date_to }}
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des bons de commande -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Fournisseur</th>
                                <th>Date commande</th>
                                <th>Livraison prévue</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Créé par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in page_obj %}
                            <tr>
                                <td><strong>{{ order.number }}</strong></td>
                                <td>
                                    <a href="{% url 'procurement:supplier_detail' order.supplier.pk %}">
                                        {{ order.supplier.name }}
                                    </a>
                                </td>
                                <td>{{ order.order_date|date:"d/m/Y" }}</td>
                                <td>{{ order.expected_delivery_date|date:"d/m/Y"|default:"-" }}</td>
                                <td>{{ order.total_amount|floatformat:2 }} €</td>
                                <td>
                                    {% if order.status == 'draft' %}
                                        <span class="badge bg-secondary">Brouillon</span>
                                    {% elif order.status == 'sent' %}
                                        <span class="badge bg-primary">Envoyé</span>
                                    {% elif order.status == 'confirmed' %}
                                        <span class="badge bg-info">Confirmé</span>
                                    {% elif order.status == 'partially_received' %}
                                        <span class="badge bg-warning">Partiellement reçu</span>
                                    {% elif order.status == 'fully_received' %}
                                        <span class="badge bg-success">Entièrement reçu</span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">Annulé</span>
                                    {% endif %}
                                </td>
                                <td>{{ order.created_by.get_full_name }}</td>
                                <td>
                                    <a href="{% url 'procurement:purchase_order_detail' order.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    {% if order.status == 'draft' %}
                                    <a href="{% url 'procurement:purchase_order_edit' order.pk %}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </a>
                                    {% endif %}
                                    {% if order.status in 'sent,confirmed,partially_received' %}
                                    <a href="{% url 'documents:reception_create' %}?purchase_order={{ order.pk }}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-truck"></i> Réceptionner
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navigation des pages">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-cart display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun bon de commande trouvé</h3>
                    <p class="text-muted">Commencez par créer votre premier bon de commande.</p>
                    {% if user.can_manage_central_warehouse %}
                    <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouveau BC
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <p class="mb-0">Total BC</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cart display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ draft_count|default:0 }}</h4>
                        <p class="mb-0">Brouillons</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ sent_count|default:0 }}</h4>
                        <p class="mb-0">Envoyés</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ received_count|default:0 }}</h4>
                        <p class="mb-0">Reçus</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
