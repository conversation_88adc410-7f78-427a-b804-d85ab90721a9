from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Sum
from django.core.paginator import Paginator
from django.db import transaction
from django.utils import timezone
from accounts.decorators import storekeeper_required, can_request_products, manager_required
from .models import InternalService, InternalOrder, InternalOrderLine, DeliveryNote
from .forms import (InternalServiceForm, InternalOrderForm, InternalOrderLineFormSet,
                   DeliveryNoteForm, InternalOrderSearchForm,
                   DeliveryNoteSearchForm, OrderApprovalForm)
from inventory.models import Stock, Warehouse
from documents.models import StockMovement


@login_required
def service_list(request):
    """Liste des services internes"""
    services = InternalService.objects.filter(is_active=True).order_by('name')
    return render(request, 'distribution/service_list.html', {'services': services})


@login_required
@storekeeper_required
def service_create(request):
    """Création d'un service interne"""
    return render(request, 'distribution/service_form.html')


@login_required
@can_request_products
def internal_order_list(request):
    """Liste des commandes internes"""
    form = InternalOrderSearchForm(request.GET)
    orders = InternalOrder.objects.select_related(
        'requesting_service', 'created_by', 'approved_by'
    )

    # Filtrer selon les permissions de l'utilisateur
    if request.user.role == 'service_requester':
        orders = orders.filter(requesting_service__manager=request.user)

    if form.is_valid():
        search = form.cleaned_data.get('search')
        requesting_service = form.cleaned_data.get('requesting_service')
        status = form.cleaned_data.get('status')
        priority = form.cleaned_data.get('priority')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            orders = orders.filter(number__icontains=search)
        if requesting_service:
            orders = orders.filter(requesting_service=requesting_service)
        if status:
            orders = orders.filter(status=status)
        if priority:
            orders = orders.filter(priority=priority)
        if date_from:
            orders = orders.filter(created_at__date__gte=date_from)
        if date_to:
            orders = orders.filter(created_at__date__lte=date_to)

    orders = orders.order_by('-created_at')
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'distribution/internal_order_list.html', {
        'page_obj': page_obj,
        'form': form
    })


@login_required
@can_request_products
def internal_order_create(request):
    """Création d'une commande interne"""
    if request.method == 'POST':
        form = InternalOrderForm(request.POST, user=request.user)
        formset = InternalOrderLineFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()

            formset.instance = order
            formset.save()

            messages.success(request, f'Commande interne "{order.number}" créée avec succès.')
            return redirect('distribution:internal_order_detail', pk=order.pk)
    else:
        form = InternalOrderForm(user=request.user)
        formset = InternalOrderLineFormSet()

    return render(request, 'distribution/internal_order_form.html', {
        'form': form,
        'formset': formset
    })


@login_required
def internal_order_detail(request, pk):
    """Détail d'une commande interne"""
    order = get_object_or_404(InternalOrder, pk=pk)

    # Vérifier les permissions
    if request.user.role == 'service_requester' and order.requesting_service.manager != request.user:
        messages.error(request, "Vous n'avez pas accès à cette commande.")
        return redirect('distribution:internal_order_list')

    lines = order.lines.select_related('product').all()
    delivery_notes = order.deliverynote_set.all().order_by('-created_at')

    return render(request, 'distribution/internal_order_detail.html', {
        'order': order,
        'lines': lines,
        'delivery_notes': delivery_notes
    })


@login_required
@can_request_products
def internal_order_edit(request, pk):
    """Modification d'une commande interne"""
    order = get_object_or_404(InternalOrder, pk=pk)

    # Vérifier les permissions
    if request.user.role == 'service_requester' and order.requesting_service.manager != request.user:
        messages.error(request, "Vous n'avez pas accès à cette commande.")
        return redirect('distribution:internal_order_list')

    if order.status != 'draft':
        messages.error(request, 'Cette commande ne peut plus être modifiée.')
        return redirect('distribution:internal_order_detail', pk=pk)

    if request.method == 'POST':
        form = InternalOrderForm(request.POST, instance=order, user=request.user)
        formset = InternalOrderLineFormSet(request.POST, instance=order)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()

            messages.success(request, f'Commande interne "{order.number}" modifiée avec succès.')
            return redirect('distribution:internal_order_detail', pk=order.pk)
    else:
        form = InternalOrderForm(instance=order, user=request.user)
        formset = InternalOrderLineFormSet(instance=order)

    return render(request, 'distribution/internal_order_form.html', {
        'form': form,
        'formset': formset,
        'order': order
    })


@login_required
@manager_required
def internal_order_approve(request, pk):
    """Approbation d'une commande interne"""
    order = get_object_or_404(InternalOrder, pk=pk)

    if order.status != 'pending_approval':
        messages.error(request, 'Cette commande ne peut plus être approuvée.')
        return redirect('distribution:internal_order_detail', pk=pk)

    if request.method == 'POST':
        form = OrderApprovalForm(request.POST)
        if form.is_valid():
            action = form.cleaned_data['action']
            notes = form.cleaned_data['approval_notes']

            if action == 'approve':
                order.status = 'approved'
                order.approved_by = request.user
                order.approval_notes = notes
                order.save()
                messages.success(request, f'Commande "{order.number}" approuvée avec succès.')
            else:  # reject
                order.status = 'rejected'
                order.approved_by = request.user
                order.approval_notes = notes
                order.save()
                messages.warning(request, f'Commande "{order.number}" rejetée.')

            return redirect('distribution:internal_order_detail', pk=pk)
    else:
        form = OrderApprovalForm()

    return render(request, 'distribution/internal_order_approve.html', {
        'order': order,
        'form': form
    })


@login_required
@manager_required
def internal_order_reject(request, pk):
    """Rejet d'une commande interne"""
    order = get_object_or_404(InternalOrder, pk=pk)

    if order.status != 'pending_approval':
        messages.error(request, 'Cette commande ne peut plus être rejetée.')
        return redirect('distribution:internal_order_detail', pk=pk)

    if request.method == 'POST':
        form = OrderApprovalForm(request.POST)
        if form.is_valid():
            with transaction.atomic():
                order.status = 'rejected'
                order.approved_by = request.user
                order.approval_date = timezone.now()
                order.rejection_reason = form.cleaned_data['approval_notes']
                order.save()

                messages.success(request, f'Commande {order.number} rejetée avec succès.')
                return redirect('distribution:internal_order_detail', pk=pk)
    else:
        form = OrderApprovalForm()

    return render(request, 'distribution/internal_order_approve.html', {
        'order': order,
        'form': form,
        'action': 'reject',
        'title': f'Rejeter la commande {order.number}'
    })


@login_required
@storekeeper_required
def internal_order_manage(request):
    """Gestion des commandes internes"""
    form = InternalOrderSearchForm(request.GET)
    orders = InternalOrder.objects.select_related(
        'requesting_service', 'created_by', 'approved_by'
    ).filter(status__in=['pending_approval', 'approved'])

    if form.is_valid():
        search = form.cleaned_data.get('search')
        requesting_service = form.cleaned_data.get('requesting_service')
        status = form.cleaned_data.get('status')
        priority = form.cleaned_data.get('priority')

        if search:
            orders = orders.filter(number__icontains=search)
        if requesting_service:
            orders = orders.filter(requesting_service=requesting_service)
        if status:
            orders = orders.filter(status=status)
        if priority:
            orders = orders.filter(priority=priority)

    orders = orders.order_by('-created_at')
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'distribution/internal_order_manage.html', {
        'page_obj': page_obj,
        'form': form
    })


@login_required
@storekeeper_required
def delivery_note_list(request):
    """Liste des bons de livraison"""
    notes = DeliveryNote.objects.all().order_by('-created_at')
    return render(request, 'distribution/delivery_note_list.html', {'notes': notes})


@login_required
@storekeeper_required
def delivery_note_create(request, order_id):
    """Création d'un bon de livraison"""
    order = get_object_or_404(InternalOrder, pk=order_id)
    return render(request, 'distribution/delivery_note_form.html', {'order': order})


@login_required
@storekeeper_required
def delivery_note_detail(request, pk):
    """Détail d'un bon de livraison"""
    note = get_object_or_404(DeliveryNote, pk=pk)
    return render(request, 'distribution/delivery_note_detail.html', {'note': note})
