from django.contrib import admin
from .models import Supplier, Contract, PurchaseOrder, PurchaseOrderLine


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'contact_person', 'email', 'phone', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'code', 'contact_person')
    ordering = ('name',)


@admin.register(Contract)
class ContractAdmin(admin.ModelAdmin):
    list_display = ('number', 'supplier', 'title', 'start_date', 'end_date', 'status', 'total_amount')
    list_filter = ('status', 'supplier')
    search_fields = ('number', 'title')
    ordering = ('-created_at',)


class PurchaseOrderLineInline(admin.TabularInline):
    model = PurchaseOrderLine
    extra = 1
    readonly_fields = ('line_total',)


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ('number', 'supplier', 'order_date', 'status', 'total_amount', 'created_by')
    list_filter = ('status', 'supplier')
    search_fields = ('number',)
    ordering = ('-created_at',)
    inlines = [PurchaseOrderLineInline]
