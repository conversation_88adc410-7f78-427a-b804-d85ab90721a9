# Generated by Django 5.2.6 on 2025-09-13 21:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="Nom"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Catégorie de produit",
                "verbose_name_plural": "Catégories de produits",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Nom du produit"),
                ),
                (
                    "reference",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Référence"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "unit",
                    models.CharField(
                        default="pièce", max_length=20, verbose_name="Unité"
                    ),
                ),
                (
                    "minimum_stock",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Stock minimum"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Actif")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.productcategory",
                        verbose_name="Catégorie",
                    ),
                ),
            ],
            options={
                "verbose_name": "Produit",
                "verbose_name_plural": "Produits",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Warehouse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="Nom du magasin"),
                ),
                (
                    "code",
                    models.CharField(max_length=20, unique=True, verbose_name="Code"),
                ),
                (
                    "warehouse_type",
                    models.CharField(
                        choices=[
                            ("central", "Magasin Central"),
                            ("sub", "Sous-Magasin"),
                        ],
                        max_length=10,
                        verbose_name="Type de magasin",
                    ),
                ),
                ("address", models.TextField(blank=True, verbose_name="Adresse")),
                ("is_active", models.BooleanField(default=True, verbose_name="Actif")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "manager",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Gérant",
                    ),
                ),
                (
                    "parent_warehouse",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.warehouse",
                        verbose_name="Magasin parent",
                    ),
                ),
            ],
            options={
                "verbose_name": "Magasin",
                "verbose_name_plural": "Magasins",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="StockTransfer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro de transfert"
                    ),
                ),
                ("transfer_date", models.DateField(verbose_name="Date de transfert")),
                (
                    "expected_arrival_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date d'arrivée prévue"
                    ),
                ),
                (
                    "actual_arrival_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Date d'arrivée réelle"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Brouillon"),
                            ("pending", "En attente"),
                            ("in_transit", "En transit"),
                            ("completed", "Terminé"),
                            ("cancelled", "Annulé"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approuvé par",
                    ),
                ),
                (
                    "received_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="received_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Reçu par",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="requested_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Demandé par",
                    ),
                ),
                (
                    "sent_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sent_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Expédié par",
                    ),
                ),
                (
                    "from_warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="outgoing_transfers",
                        to="inventory.warehouse",
                        verbose_name="Magasin source",
                    ),
                ),
                (
                    "to_warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="incoming_transfers",
                        to="inventory.warehouse",
                        verbose_name="Magasin destination",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transfert de stock",
                "verbose_name_plural": "Transferts de stock",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StockTransferLine",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity_requested",
                    models.PositiveIntegerField(verbose_name="Quantité demandée"),
                ),
                (
                    "quantity_sent",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité expédiée"
                    ),
                ),
                (
                    "quantity_received",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité reçue"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
                (
                    "transfer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lines",
                        to="inventory.stocktransfer",
                        verbose_name="Transfert",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ligne de transfert",
                "verbose_name_plural": "Lignes de transfert",
                "unique_together": {("transfer", "product")},
            },
        ),
        migrations.CreateModel(
            name="Stock",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(default=0, verbose_name="Quantité"),
                ),
                (
                    "reserved_quantity",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Quantité réservée"
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.warehouse",
                        verbose_name="Magasin",
                    ),
                ),
            ],
            options={
                "verbose_name": "Stock",
                "verbose_name_plural": "Stocks",
                "ordering": ["warehouse", "product"],
                "unique_together": {("warehouse", "product")},
            },
        ),
    ]
