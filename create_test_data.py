#!/usr/bin/env python
"""
Script pour créer des données de test pour l'application GPN
"""
import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cleaning_management.settings')
django.setup()

from accounts.models import User
from inventory.models import ProductCategory, Product, Warehouse, Stock
from procurement.models import Supplier, Contract, PurchaseOrder, PurchaseOrderLine
from distribution.models import InternalService


def create_test_data():
    print("Création des données de test...")
    
    # Créer des utilisateurs de test
    print("- Création des utilisateurs...")
    
    # Manager
    manager, created = User.objects.get_or_create(
        username='manager',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'role': 'manager',
            'department': 'Direction',
            'is_staff': True
        }
    )
    if created:
        manager.set_password('manager123')
        manager.save()
        print("  - Manager créé")
    
    # Magasinier central
    storekeeper, created = User.objects.get_or_create(
        username='magasinier',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'role': 'central_storekeeper',
            'department': 'Logistique'
        }
    )
    if created:
        storekeeper.set_password('magasinier123')
        storekeeper.save()
        print("  - Magasinier central créé")
    
    # Gérant sous-magasin
    sub_manager, created = User.objects.get_or_create(
        username='gerant',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Pierre',
            'last_name': 'Durand',
            'role': 'sub_storekeeper',
            'department': 'Maintenance'
        }
    )
    if created:
        sub_manager.set_password('gerant123')
        sub_manager.save()
        print("  - Gérant sous-magasin créé")
    
    # Service demandeur
    requester, created = User.objects.get_or_create(
        username='demandeur',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Sophie',
            'last_name': 'Bernard',
            'role': 'service_requester',
            'department': 'Nettoyage'
        }
    )
    if created:
        requester.set_password('demandeur123')
        requester.save()
        print("  - Service demandeur créé")
    
    # Créer des catégories de produits
    print("- Création des catégories de produits...")
    categories_data = [
        ('Détergents', 'Produits de nettoyage liquides'),
        ('Désinfectants', 'Produits de désinfection'),
        ('Matériel', 'Matériel de nettoyage'),
        ('Consommables', 'Produits consommables')
    ]
    
    categories = {}
    for name, description in categories_data:
        category, created = ProductCategory.objects.get_or_create(
            name=name,
            defaults={'description': description}
        )
        categories[name] = category
        if created:
            print(f"  - Catégorie '{name}' créée")
    
    # Créer des produits
    print("- Création des produits...")
    products_data = [
        ('DET001', 'Détergent multi-surfaces', 'Détergents', 'litre', 10),
        ('DET002', 'Liquide vaisselle', 'Détergents', 'litre', 5),
        ('DES001', 'Désinfectant sol', 'Désinfectants', 'litre', 8),
        ('DES002', 'Gel hydroalcoolique', 'Désinfectants', 'litre', 15),
        ('MAT001', 'Serpillière microfibre', 'Matériel', 'pièce', 20),
        ('MAT002', 'Gants de ménage', 'Matériel', 'paire', 50),
        ('CON001', 'Sacs poubelle 50L', 'Consommables', 'rouleau', 10),
        ('CON002', 'Papier essuie-tout', 'Consommables', 'rouleau', 25),
    ]
    
    products = {}
    for reference, name, category_name, unit, min_stock in products_data:
        product, created = Product.objects.get_or_create(
            reference=reference,
            defaults={
                'name': name,
                'category': categories[category_name],
                'unit': unit,
                'minimum_stock': min_stock
            }
        )
        products[reference] = product
        if created:
            print(f"  - Produit '{name}' créé")
    
    # Créer des magasins
    print("- Création des magasins...")
    
    # Magasin central
    central_warehouse, created = Warehouse.objects.get_or_create(
        code='CENTRAL',
        defaults={
            'name': 'Magasin Central',
            'warehouse_type': 'central',
            'manager': storekeeper,
            'address': '123 Rue de la Logistique, 75001 Paris'
        }
    )
    if created:
        print("  - Magasin central créé")
    
    # Sous-magasin
    sub_warehouse, created = Warehouse.objects.get_or_create(
        code='SUB001',
        defaults={
            'name': 'Sous-magasin Maintenance',
            'warehouse_type': 'sub',
            'parent_warehouse': central_warehouse,
            'manager': sub_manager,
            'address': 'Bâtiment B, Étage 2'
        }
    )
    if created:
        print("  - Sous-magasin créé")
    
    # Créer des stocks
    print("- Création des stocks...")
    for product in products.values():
        # Stock central
        stock_central, created = Stock.objects.get_or_create(
            warehouse=central_warehouse,
            product=product,
            defaults={'quantity': product.minimum_stock * 3}
        )
        if created:
            print(f"  - Stock central pour {product.name}")
        
        # Stock sous-magasin (quantité plus faible)
        stock_sub, created = Stock.objects.get_or_create(
            warehouse=sub_warehouse,
            product=product,
            defaults={'quantity': max(1, product.minimum_stock // 2)}
        )
        if created:
            print(f"  - Stock sous-magasin pour {product.name}")
    
    # Créer des fournisseurs
    print("- Création des fournisseurs...")
    suppliers_data = [
        ('CLEAN001', 'CleanPro Solutions', 'Marie Dubois', '<EMAIL>', '01.23.45.67.89'),
        ('HYGIE001', 'Hygiène Plus', 'Jean Leclerc', '<EMAIL>', '***********.32'),
        ('MATER001', 'Matériel Service', 'Pierre Moreau', '<EMAIL>', '***********.44'),
    ]
    
    suppliers = {}
    for code, name, contact, email, phone in suppliers_data:
        supplier, created = Supplier.objects.get_or_create(
            code=code,
            defaults={
                'name': name,
                'contact_person': contact,
                'email': email,
                'phone': phone,
                'address': f'Adresse de {name}'
            }
        )
        suppliers[code] = supplier
        if created:
            print(f"  - Fournisseur '{name}' créé")
    
    # Créer des services internes
    print("- Création des services internes...")
    services_data = [
        ('NETT', 'Service Nettoyage', requester),
        ('MAINT', 'Service Maintenance', sub_manager),
        ('ADMIN', 'Administration', manager),
    ]
    
    for code, name, manager_user in services_data:
        service, created = InternalService.objects.get_or_create(
            code=code,
            defaults={
                'name': name,
                'manager': manager_user,
                'description': f'Description du {name}'
            }
        )
        if created:
            print(f"  - Service '{name}' créé")
    
    # Créer des contrats et bons de commande de test
    print("- Création des contrats et bons de commande...")

    # Contrat avec CleanPro
    contract1, created = Contract.objects.get_or_create(
        number='CONT-2024-001',
        defaults={
            'supplier': suppliers['CLEAN001'],
            'title': 'Contrat produits de nettoyage 2024',
            'start_date': date(2024, 1, 1),
            'end_date': date(2024, 12, 31),
            'status': 'active',
            'total_amount': Decimal('50000.00'),
            'description': 'Contrat annuel pour fourniture de produits de nettoyage',
            'created_by': storekeeper
        }
    )
    if created:
        print("  - Contrat CleanPro créé")

    # Bon de commande
    po1, created = PurchaseOrder.objects.get_or_create(
        number='BC-2024-001',
        defaults={
            'supplier': suppliers['CLEAN001'],
            'contract': contract1,
            'order_date': date.today() - timedelta(days=7),
            'expected_delivery_date': date.today() + timedelta(days=3),
            'status': 'sent',
            'created_by': storekeeper,
            'notes': 'Commande urgente pour réapprovisionnement'
        }
    )
    if created:
        print("  - Bon de commande BC-2024-001 créé")

        # Lignes du bon de commande
        PurchaseOrderLine.objects.get_or_create(
            purchase_order=po1,
            product=products['DET001'],
            defaults={
                'quantity_ordered': 50,
                'unit_price': Decimal('12.50')
            }
        )
        PurchaseOrderLine.objects.get_or_create(
            purchase_order=po1,
            product=products['DET002'],
            defaults={
                'quantity_ordered': 30,
                'unit_price': Decimal('8.75')
            }
        )
        PurchaseOrderLine.objects.get_or_create(
            purchase_order=po1,
            product=products['DES001'],
            defaults={
                'quantity_ordered': 25,
                'unit_price': Decimal('15.20')
            }
        )

        # Calculer le montant total
        total = sum(line.line_total for line in po1.lines.all())
        po1.total_amount = total
        po1.save()
        print("  - Lignes du BC ajoutées")

    print("\nDonnées de test créées avec succès!")
    print("\nComptes utilisateurs créés:")
    print("- admin / admin123 (Administrateur)")
    print("- manager / manager123 (Manager)")
    print("- magasinier / magasinier123 (Magasinier central)")
    print("- gerant / gerant123 (Gérant sous-magasin)")
    print("- demandeur / demandeur123 (Service demandeur)")
    print("\nDonnées créées:")
    print(f"- {ProductCategory.objects.count()} catégories de produits")
    print(f"- {Product.objects.count()} produits")
    print(f"- {Warehouse.objects.count()} magasins")
    print(f"- {Supplier.objects.count()} fournisseurs")
    print(f"- {Contract.objects.count()} contrats")
    print(f"- {PurchaseOrder.objects.count()} bons de commande")
    print(f"- {Stock.objects.count()} stocks")


if __name__ == '__main__':
    create_test_data()
