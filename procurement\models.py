from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal
from inventory.models import Product, Warehouse

User = get_user_model()


class Supplier(models.Model):
    """Fournisseur"""
    name = models.CharField(max_length=200, verbose_name="Nom du fournisseur")
    code = models.CharField(max_length=50, unique=True, verbose_name="Code fournisseur")
    contact_person = models.CharField(max_length=100, blank=True, verbose_name="Personne de contact")
    email = models.EmailField(blank=True, verbose_name="Email")
    phone = models.CharField(max_length=20, blank=True, verbose_name="Téléphone")
    address = models.TextField(blank=True, verbose_name="Adresse")
    is_active = models.BooleanField(default=True, verbose_name="Actif")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Fournisseur"
        verbose_name_plural = "Fournisseurs"
        ordering = ['name']

    def __str__(self):
        return self.name


class Contract(models.Model):
    """Contrat avec fournisseur"""
    CONTRACT_STATUS = [
        ('draft', 'Brouillon'),
        ('active', 'Actif'),
        ('expired', 'Expiré'),
        ('terminated', 'Résilié'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro de contrat")
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, verbose_name="Fournisseur")
    title = models.CharField(max_length=200, verbose_name="Titre du contrat")
    start_date = models.DateField(verbose_name="Date de début")
    end_date = models.DateField(verbose_name="Date de fin")
    status = models.CharField(max_length=20, choices=CONTRACT_STATUS, default='draft', verbose_name="Statut")
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="Montant total"
    )
    description = models.TextField(blank=True, verbose_name="Description")
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="Créé par")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Contrat"
        verbose_name_plural = "Contrats"
        ordering = ['-created_at']

    def __str__(self):
        return f"Contrat {self.number} - {self.supplier.name}"


class PurchaseOrder(models.Model):
    """Bon de commande"""
    ORDER_STATUS = [
        ('draft', 'Brouillon'),
        ('sent', 'Envoyé'),
        ('confirmed', 'Confirmé'),
        ('partially_received', 'Partiellement reçu'),
        ('fully_received', 'Entièrement reçu'),
        ('cancelled', 'Annulé'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro de BC")
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, verbose_name="Fournisseur")
    contract = models.ForeignKey(
        Contract,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Contrat associé"
    )
    order_date = models.DateField(verbose_name="Date de commande")
    expected_delivery_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Date de livraison prévue"
    )
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='draft', verbose_name="Statut")
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Montant total"
    )
    notes = models.TextField(blank=True, verbose_name="Notes")
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="Créé par")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Bon de commande"
        verbose_name_plural = "Bons de commande"
        ordering = ['-created_at']

    def __str__(self):
        return f"BC {self.number} - {self.supplier.name}"


class PurchaseOrderLine(models.Model):
    """Ligne de bon de commande"""
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name="Bon de commande"
    )
    product = models.ForeignKey(Product, on_delete=models.PROTECT, verbose_name="Produit")
    quantity_ordered = models.PositiveIntegerField(verbose_name="Quantité commandée")
    quantity_received = models.PositiveIntegerField(default=0, verbose_name="Quantité reçue")
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Prix unitaire"
    )
    line_total = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name="Total ligne"
    )

    class Meta:
        verbose_name = "Ligne de commande"
        verbose_name_plural = "Lignes de commande"
        unique_together = ['purchase_order', 'product']

    def save(self, *args, **kwargs):
        self.line_total = self.quantity_ordered * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.name} - {self.quantity_ordered} {self.product.unit}"

    @property
    def quantity_remaining(self):
        """Quantité restante à recevoir"""
        return self.quantity_ordered - self.quantity_received

    @property
    def is_fully_received(self):
        """Vérifie si la ligne est entièrement reçue"""
        return self.quantity_received >= self.quantity_ordered
