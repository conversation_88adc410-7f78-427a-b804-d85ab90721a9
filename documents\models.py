from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import uuid

User = get_user_model()


class Document(models.Model):
    """Document stocké en BLOB dans la base de données"""
    DOCUMENT_TYPES = [
        ('purchase_order', 'Bon de commande'),
        ('contract', 'Contrat'),
        ('delivery_receipt', 'Bon de réception'),
        ('internal_order', 'BCN'),
        ('delivery_note', 'Bon de livraison'),
        ('invoice', 'Facture'),
        ('other', 'Autre'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, verbose_name="Nom du document")
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name="Type de document"
    )
    file_data = models.BinaryField(verbose_name="Données du fichier")
    file_name = models.CharField(max_length=255, verbose_name="Nom du fichier")
    file_size = models.PositiveIntegerField(verbose_name="Taille du fichier (bytes)")
    mime_type = models.CharField(max_length=100, verbose_name="Type MIME")

    # Relation générique pour associer le document à n'importe quel modèle
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    description = models.TextField(blank=True, verbose_name="Description")
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name="Téléchargé par"
    )
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de téléchargement")

    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.name} ({self.get_document_type_display()})"

    @property
    def file_size_mb(self):
        """Taille du fichier en MB"""
        return round(self.file_size / (1024 * 1024), 2)

    @property
    def is_image(self):
        """Vérifie si le document est une image"""
        return self.mime_type.startswith('image/')

    @property
    def is_pdf(self):
        """Vérifie si le document est un PDF"""
        return self.mime_type == 'application/pdf'


class StockMovement(models.Model):
    """Mouvement de stock pour traçabilité"""
    MOVEMENT_TYPES = [
        ('reception', 'Réception'),
        ('transfer_out', 'Transfert sortant'),
        ('transfer_in', 'Transfert entrant'),
        ('distribution', 'Distribution'),
        ('adjustment', 'Ajustement'),
        ('return', 'Retour'),
    ]

    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name="Type de mouvement"
    )
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        verbose_name="Produit"
    )
    warehouse = models.ForeignKey(
        'inventory.Warehouse',
        on_delete=models.PROTECT,
        verbose_name="Magasin"
    )
    quantity = models.IntegerField(verbose_name="Quantité")  # Peut être négatif pour les sorties
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="Numéro de référence"
    )
    notes = models.TextField(blank=True, verbose_name="Notes")

    # Relation générique pour associer le mouvement à son document source
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name="Créé par"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")

    class Meta:
        verbose_name = "Mouvement de stock"
        verbose_name_plural = "Mouvements de stock"
        ordering = ['-created_at']

    def __str__(self):
        sign = "+" if self.quantity > 0 else ""
        return f"{self.product.name} - {sign}{self.quantity} ({self.get_movement_type_display()})"


class Reception(models.Model):
    """Réception de marchandises"""
    RECEPTION_STATUS = [
        ('draft', 'Brouillon'),
        ('validated', 'Validée'),
        ('cancelled', 'Annulée'),
    ]

    number = models.CharField(max_length=50, unique=True, verbose_name="Numéro de réception")
    purchase_order = models.ForeignKey(
        'procurement.PurchaseOrder',
        on_delete=models.PROTECT,
        verbose_name="Bon de commande"
    )
    warehouse = models.ForeignKey(
        'inventory.Warehouse',
        on_delete=models.PROTECT,
        verbose_name="Magasin de réception"
    )
    reception_date = models.DateField(verbose_name="Date de réception")
    delivery_note_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="Numéro BL fournisseur"
    )
    status = models.CharField(
        max_length=20,
        choices=RECEPTION_STATUS,
        default='draft',
        verbose_name="Statut"
    )
    notes = models.TextField(blank=True, verbose_name="Notes")
    received_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        verbose_name="Reçu par"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Réception"
        verbose_name_plural = "Réceptions"
        ordering = ['-created_at']

    def __str__(self):
        return f"Réception {self.number} - BC {self.purchase_order.number}"


class ReceptionLine(models.Model):
    """Ligne de réception"""
    reception = models.ForeignKey(
        Reception,
        on_delete=models.CASCADE,
        related_name='lines',
        verbose_name="Réception"
    )
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        verbose_name="Produit"
    )
    quantity_received = models.PositiveIntegerField(verbose_name="Quantité reçue")
    notes = models.TextField(blank=True, verbose_name="Notes")

    class Meta:
        verbose_name = "Ligne de réception"
        verbose_name_plural = "Lignes de réception"
        unique_together = ['reception', 'product']

    def __str__(self):
        return f"{self.product.name} - {self.quantity_received} {self.product.unit}"
