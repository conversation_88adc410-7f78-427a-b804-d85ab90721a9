from django.contrib import admin
from .models import Document, StockMovement, Reception, ReceptionLine


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('name', 'document_type', 'file_name', 'file_size_mb', 'uploaded_by', 'uploaded_at')
    list_filter = ('document_type', 'mime_type')
    search_fields = ('name', 'file_name')
    ordering = ('-uploaded_at',)
    readonly_fields = ('file_size_mb', 'is_image', 'is_pdf')


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ('product', 'warehouse', 'movement_type', 'quantity', 'reference_number', 'created_by', 'created_at')
    list_filter = ('movement_type', 'warehouse', 'product__category')
    search_fields = ('product__name', 'reference_number')
    ordering = ('-created_at',)


class ReceptionLineInline(admin.TabularInline):
    model = ReceptionLine
    extra = 1


@admin.register(Reception)
class ReceptionAdmin(admin.ModelAdmin):
    list_display = ('number', 'purchase_order', 'warehouse', 'reception_date', 'status', 'received_by')
    list_filter = ('status', 'warehouse')
    search_fields = ('number', 'delivery_note_number')
    ordering = ('-created_at',)
    inlines = [ReceptionLineInline]
