{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Nouvelle réception - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'documents:reception_list' %}">Réceptions</a></li>
                <li class="breadcrumb-item active">Nouvelle réception</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1><i class="bi bi-truck"></i> Nouvelle réception</h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations de la réception</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.number.id_for_label }}" class="form-label">
                                    {{ form.number.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.number }}
                                {% if form.number.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.number.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reception_date.id_for_label }}" class="form-label">
                                    {{ form.reception_date.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.reception_date }}
                                {% if form.reception_date.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.reception_date.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.purchase_order.id_for_label }}" class="form-label">
                                    {{ form.purchase_order.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.purchase_order }}
                                {% if form.purchase_order.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.purchase_order.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Sélectionnez le bon de commande correspondant à cette réception.
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">
                                    {{ form.warehouse.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.warehouse.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.delivery_note_number.id_for_label }}" class="form-label">
                            {{ form.delivery_note_number.label }}
                        </label>
                        {{ form.delivery_note_number }}
                        <small class="form-text text-muted">
                            Numéro du bon de livraison du fournisseur (optionnel).
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        <small class="form-text text-muted">
                            Notes ou observations sur cette réception.
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'documents:reception_list' %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Créer la réception
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                <h6>Processus de réception :</h6>
                <ol class="small">
                    <li>Créer la réception en sélectionnant le bon de commande</li>
                    <li>Saisir les quantités reçues pour chaque produit</li>
                    <li>Valider la réception pour mettre à jour les stocks</li>
                </ol>
                
                <hr>
                
                <h6>Informations importantes :</h6>
                <ul class="small text-muted">
                    <li>Le numéro de réception doit être unique</li>
                    <li>Seuls les BC envoyés ou confirmés peuvent être réceptionnés</li>
                    <li>La validation met automatiquement à jour les stocks</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clock-history"></i> Réceptions récentes</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    Les réceptions récentes s'afficheront ici une fois que vous en aurez créé.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-générer le numéro de réception si vide
    const numberField = document.getElementById('{{ form.number.id_for_label }}');
    if (numberField && !numberField.value) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');
        numberField.value = `REC-${year}${month}${day}-${time}`;
    }
    
    // Définir la date du jour par défaut
    const dateField = document.getElementById('{{ form.reception_date.id_for_label }}');
    if (dateField && !dateField.value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        dateField.value = `${year}-${month}-${day}`;
    }
});
</script>
{% endblock %}
