{% extends 'base.html' %}

{% block title %}Produits - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-box-seam"></i> Produits</h1>
            {% if user.can_manage_central_warehouse %}
            <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau produit
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Nom</th>
                                <th>Catégorie</th>
                                <th>Unité</th>
                                <th>Stock minimum</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in page_obj %}
                            <tr>
                                <td><strong>{{ product.reference }}</strong></td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name }}</td>
                                <td>{{ product.unit }}</td>
                                <td>{{ product.minimum_stock }}</td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'inventory:product_detail' product.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                    {% if user.can_manage_central_warehouse %}
                                    <a href="{% url 'inventory:product_edit' product.pk %}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box-seam display-1 text-muted"></i>
                    <h3 class="mt-3">Aucun produit trouvé</h3>
                    <p class="text-muted">Commencez par ajouter votre premier produit.</p>
                    {% if user.can_manage_central_warehouse %}
                    <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Nouveau produit
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
