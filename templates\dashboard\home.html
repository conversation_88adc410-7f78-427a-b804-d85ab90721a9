{% extends 'base.html' %}

{% block title %}Tableau de bord - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> Tableau de bord
            <small class="text-muted">- {{ user.get_role_display }}</small>
        </h1>
    </div>
</div>

<!-- Statistiques générales pour Admin/Manager -->
{% if user.can_view_all_data %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_products }}</h4>
                        <p class="mb-0">Produits actifs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box-seam fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_warehouses }}</h4>
                        <p class="mb-0">Magasins actifs</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ low_stock_count }}</h4>
                        <p class="mb-0">Stocks faibles</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_orders }}</h4>
                        <p class="mb-0">Demandes en attente</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mouvements récents -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-arrow-left-right"></i> Mouvements récents</h5>
            </div>
            <div class="card-body">
                {% if recent_movements %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Produit</th>
                                <th>Magasin</th>
                                <th>Type</th>
                                <th>Quantité</th>
                                <th>Créé par</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                            <tr>
                                <td>{{ movement.created_at|date:"d/m/Y H:i" }}</td>
                                <td>{{ movement.product.name }}</td>
                                <td>{{ movement.warehouse.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if movement.quantity > 0 %}
                                        <span class="text-success">+{{ movement.quantity }}</span>
                                    {% else %}
                                        <span class="text-danger">{{ movement.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ movement.created_by.get_full_name }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucun mouvement récent.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistiques pour Magasinier Central -->
{% if user.can_manage_central_warehouse and not user.can_view_all_data %}
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_receptions }}</h4>
                        <p class="mb-0">Réceptions en attente</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-truck fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_internal_orders }}</h4>
                        <p class="mb-0">BCN à traiter</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ my_warehouses.count }}</h4>
                        <p class="mb-0">Mes magasins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistiques pour Gérant de sous-magasin -->
{% if user.can_manage_sub_warehouse and not user.can_manage_central_warehouse %}
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ my_stock_count }}</h4>
                        <p class="mb-0">Produits en stock</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ my_low_stock }}</h4>
                        <p class="mb-0">Stocks faibles</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ my_warehouses.count }}</h4>
                        <p class="mb-0">Mes magasins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistiques pour Service demandeur -->
{% if user.can_request_products and not user.can_manage_sub_warehouse %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_orders_count }}</h4>
                        <p class="mb-0">Mes demandes en cours</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard-data fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <a href="{% url 'distribution:internal_order_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle"></i> Nouvelle demande
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Mes dernières demandes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list-ul"></i> Mes dernières demandes</h5>
            </div>
            <div class="card-body">
                {% if my_orders %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Priorité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in my_orders %}
                            <tr>
                                <td>{{ order.number }}</td>
                                <td>{{ order.request_date|date:"d/m/Y" }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ order.get_priority_display }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'distribution:internal_order_detail' order.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> Voir
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucune demande récente.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions rapides -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> Actions rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if user.can_request_products %}
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'distribution:internal_order_create' %}" class="btn btn-primary w-100">
                            <i class="bi bi-plus-circle"></i> Nouvelle demande
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if user.can_manage_central_warehouse %}
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-success w-100">
                            <i class="bi bi-cart-plus"></i> Nouveau BC
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'documents:reception_create' %}" class="btn btn-info w-100">
                            <i class="bi bi-truck"></i> Nouvelle réception
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if user.can_manage_sub_warehouse %}
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:transfer_create' %}" class="btn btn-warning w-100">
                            <i class="bi bi-arrow-left-right"></i> Transfert
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
