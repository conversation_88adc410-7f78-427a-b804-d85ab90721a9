# Generated by Django 5.2.6 on 2025-09-13 21:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("inventory", "0001_initial"),
        ("procurement", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, verbose_name="Nom du document"),
                ),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("purchase_order", "Bon de commande"),
                            ("contract", "Contrat"),
                            ("delivery_receipt", "Bon de réception"),
                            ("internal_order", "BCN"),
                            ("delivery_note", "Bon de livraison"),
                            ("invoice", "Facture"),
                            ("other", "Autre"),
                        ],
                        max_length=20,
                        verbose_name="Type de document",
                    ),
                ),
                ("file_data", models.BinaryField(verbose_name="Données du fichier")),
                (
                    "file_name",
                    models.CharField(max_length=255, verbose_name="Nom du fichier"),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(
                        verbose_name="Taille du fichier (bytes)"
                    ),
                ),
                (
                    "mime_type",
                    models.CharField(max_length=100, verbose_name="Type MIME"),
                ),
                ("object_id", models.PositiveIntegerField()),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "uploaded_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de téléchargement"
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Téléchargé par",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document",
                "verbose_name_plural": "Documents",
                "ordering": ["-uploaded_at"],
            },
        ),
        migrations.CreateModel(
            name="Reception",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Numéro de réception"
                    ),
                ),
                ("reception_date", models.DateField(verbose_name="Date de réception")),
                (
                    "delivery_note_number",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Numéro BL fournisseur"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Brouillon"),
                            ("validated", "Validée"),
                            ("cancelled", "Annulée"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="Statut",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="procurement.purchaseorder",
                        verbose_name="Bon de commande",
                    ),
                ),
                (
                    "received_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Reçu par",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.warehouse",
                        verbose_name="Magasin de réception",
                    ),
                ),
            ],
            options={
                "verbose_name": "Réception",
                "verbose_name_plural": "Réceptions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StockMovement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "movement_type",
                    models.CharField(
                        choices=[
                            ("reception", "Réception"),
                            ("transfer_out", "Transfert sortant"),
                            ("transfer_in", "Transfert entrant"),
                            ("distribution", "Distribution"),
                            ("adjustment", "Ajustement"),
                            ("return", "Retour"),
                        ],
                        max_length=20,
                        verbose_name="Type de mouvement",
                    ),
                ),
                ("quantity", models.IntegerField(verbose_name="Quantité")),
                (
                    "reference_number",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Numéro de référence"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("object_id", models.PositiveIntegerField()),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Date de création"
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Créé par",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.warehouse",
                        verbose_name="Magasin",
                    ),
                ),
            ],
            options={
                "verbose_name": "Mouvement de stock",
                "verbose_name_plural": "Mouvements de stock",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReceptionLine",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity_received",
                    models.PositiveIntegerField(verbose_name="Quantité reçue"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="inventory.product",
                        verbose_name="Produit",
                    ),
                ),
                (
                    "reception",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="lines",
                        to="documents.reception",
                        verbose_name="Réception",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ligne de réception",
                "verbose_name_plural": "Lignes de réception",
                "unique_together": {("reception", "product")},
            },
        ),
    ]
