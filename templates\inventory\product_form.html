{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Modifier{% else %}Nouveau{% endif %} produit - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:home' %}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:product_list' %}">Produits</a></li>
                {% if form.instance.pk %}
                <li class="breadcrumb-item"><a href="{% url 'inventory:product_detail' form.instance.pk %}">{{ form.instance.name }}</a></li>
                <li class="breadcrumb-item active">Modifier</li>
                {% else %}
                <li class="breadcrumb-item active">Nouveau produit</li>
                {% endif %}
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1>
            <i class="bi bi-box-seam"></i>
            {% if form.instance.pk %}
                Modifier le produit
            {% else %}
                Nouveau produit
            {% endif %}
        </h1>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- Informations de base -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations de base</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reference.id_for_label }}" class="form-label">
                                    {{ form.reference.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.reference }}
                                {% if form.reference.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.reference.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Référence unique du produit (ex: PROD001)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.category.id_for_label }}" class="form-label">
                                    {{ form.category.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.category.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.unit.id_for_label }}" class="form-label">
                                    {{ form.unit.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.unit }}
                                {% if form.unit.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.unit.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Unité de mesure (ex: litre, kg, pièce)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">
                            {{ form.description.label }}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.description.errors.0 }}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Description détaillée du produit
                        </small>
                    </div>
                </div>
            </div>

            <!-- Gestion des stocks -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> Gestion des stocks</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.minimum_stock.id_for_label }}" class="form-label">
                                    {{ form.minimum_stock.label }}
                                </label>
                                {{ form.minimum_stock }}
                                {% if form.minimum_stock.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.minimum_stock.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Seuil d'alerte pour le réapprovisionnement
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.is_active.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Décocher pour désactiver le produit
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Erreurs générales du formulaire -->
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
            
            <div class="d-flex justify-content-between">
                <a href="{% if form.instance.pk %}{% url 'inventory:product_detail' form.instance.pk %}{% else %}{% url 'inventory:product_list' %}{% endif %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Annuler
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i> 
                    {% if form.instance.pk %}Modifier{% else %}Créer{% endif %}
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Aide</h6>
            </div>
            <div class="card-body">
                {% if form.instance.pk %}
                <h6>Modification de produit :</h6>
                <ul class="small">
                    <li>Modifiez les informations nécessaires</li>
                    <li>La référence doit rester unique</li>
                    <li>Vérifiez la catégorie et l'unité</li>
                    <li>Ajustez le stock minimum si nécessaire</li>
                </ul>
                {% else %}
                <h6>Création de produit :</h6>
                <ul class="small">
                    <li>Tous les champs marqués * sont obligatoires</li>
                    <li>La référence doit être unique</li>
                    <li>Choisissez la bonne catégorie</li>
                    <li>Définissez l'unité de mesure</li>
                </ul>
                {% endif %}
                
                <hr>
                
                <h6>Bonnes pratiques :</h6>
                <ul class="small text-muted">
                    <li>Utilisez des références claires</li>
                    <li>Décrivez précisément le produit</li>
                    <li>Définissez un stock minimum réaliste</li>
                    <li>Vérifiez l'orthographe</li>
                </ul>
            </div>
        </div>
        
        {% if form.instance.pk %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-graph-up"></i> Informations stock</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-2">
                    <strong>Référence :</strong> {{ form.instance.reference }}
                </p>
                <p class="small text-muted mb-2">
                    <strong>Créé le :</strong> {{ form.instance.created_at|date:"d/m/Y" }}
                </p>
                <p class="small text-muted mb-0">
                    <strong>Modifié le :</strong> {{ form.instance.updated_at|date:"d/m/Y" }}
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
